"""
配置管理模組
管理系統配置、API密鑰和策略參數

作者: 專業量化策略工程師
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any

class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.config_dir = Path(__file__).parent.parent / "config"
        self.config = self._load_config()
        self.api_keys = self._load_api_keys()
    
    def _load_config(self) -> Dict[str, Any]:
        """載入主配置文件"""
        config_file = self.config_dir / "config.yaml"
        
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        else:
            # 返回默認配置
            return self._get_default_config()
    
    def _load_api_keys(self) -> Dict[str, Any]:
        """載入API密鑰"""
        api_file = self.config_dir / "api_keys.yaml"
        
        if api_file.exists():
            with open(api_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        else:
            # 從環境變量載入
            return {
                'blave': {
                    'api_key': os.getenv('BLAVE_API_KEY', 'acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6'),
                    'secret_key': os.getenv('BLAVE_SECRET_KEY', '5dc330fd5a40ca402111b7774266fc5c32d0941e77125a6de7956fce68b12f0d')
                },
                'bybit': {
                    'api_key': os.getenv('BYBIT_API_KEY', ''),
                    'secret_key': os.getenv('BYBIT_SECRET_KEY', '')
                },
                'telegram': {
                    'bot_token': os.getenv('TELEGRAM_BOT_TOKEN', ''),
                    'chat_id': os.getenv('TELEGRAM_CHAT_ID', '')
                }
            }
    
    def _get_default_config(self) -> Dict[str, Any]:
        """獲取默認配置"""
        return {
            'system': {
                'name': '聖杯級交易信號系統',
                'version': '1.0.0',
                'update_interval': 3600,  # 1小時
                'timezone': 'UTC'
            },
            'strategies': {
                'PEPE_1H': {
                    'symbol': '1000PEPEUSDT',
                    'timeframe': '1H',
                    'ccb_window': 12,
                    'ccb_std': 2.0,
                    'taker_lookback': 24,
                    'taker_threshold': 65,
                    'enabled': True
                },
                'XRP_1H': {
                    'symbol': 'XRPUSDT',
                    'timeframe': '1H',
                    'ccb_window': 12,
                    'ccb_std': 2.0,
                    'taker_lookback': 24,
                    'taker_threshold': 65,
                    'enabled': True
                },
                'SOL_1H': {
                    'symbol': 'SOLUSDT',
                    'timeframe': '1H',
                    'ccb_window': 12,
                    'ccb_std': 2.0,
                    'taker_lookback': 24,
                    'taker_threshold': 65,
                    'enabled': True
                }
            },
            'risk_management': {
                'max_drawdown': 0.20,
                'stop_loss': 0.05,
                'take_profit': 0.15,
                'position_size': 1.0
            },
            'data': {
                'blave_base_url': 'https://api.blave.org',
                'bybit_base_url': 'https://api.bybit.com',
                'data_retention_days': 30,
                'backup_enabled': True
            },
            'notifications': {
                'telegram_enabled': True,
                'signal_notifications': True,
                'pnl_notifications': True,
                'daily_reports': True,
                'error_alerts': True
            }
        }
    
    def get(self, key: str, default=None):
        """獲取配置值"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def get_api_key(self, service: str, key_type: str = 'api_key'):
        """獲取API密鑰"""
        return self.api_keys.get(service, {}).get(key_type, '')
    
    def get_strategy_config(self, strategy_name: str):
        """獲取策略配置"""
        return self.config.get('strategies', {}).get(strategy_name, {})
    
    def is_strategy_enabled(self, strategy_name: str) -> bool:
        """檢查策略是否啟用"""
        strategy_config = self.get_strategy_config(strategy_name)
        return strategy_config.get('enabled', False)

    def get_telegram_config(self) -> Dict[str, str]:
        """獲取Telegram配置"""
        return self.api_keys.get('telegram', {})

    def get_blave_config(self) -> Dict[str, str]:
        """獲取Blave API配置"""
        return self.api_keys.get('blave', {})

    def get_bybit_config(self) -> Dict[str, str]:
        """獲取Bybit API配置"""
        return self.api_keys.get('bybit', {})
