#!/usr/bin/env python3
"""
查看交易記錄CSV文件的工具
"""

import csv
import os
from datetime import datetime
import pandas as pd

def view_trades():
    """查看交易記錄"""
    csv_path = "data/trades_record.csv"
    
    if not os.path.exists(csv_path):
        print("❌ 交易記錄文件不存在")
        return
    
    try:
        # 讀取CSV文件
        df = pd.read_csv(csv_path)
        
        if df.empty:
            print("📊 暫無交易記錄")
            return
        
        print("📊 交易記錄總覽")
        print("=" * 80)
        
        # 統計概覽
        total_trades = len(df)
        active_trades = len(df[df['status'] == 'ACTIVE'])
        closed_trades = len(df[df['status'] == 'CLOSED'])
        
        print(f"📈 總交易數: {total_trades}")
        print(f"💼 活躍交易: {active_trades}")
        print(f"✅ 已完成: {closed_trades}")
        
        if closed_trades > 0:
            closed_df = df[df['status'] == 'CLOSED'].copy()
            closed_df['pnl_pct'] = pd.to_numeric(closed_df['pnl_pct'], errors='coerce')
            
            total_pnl = closed_df['pnl_pct'].sum()
            win_trades = len(closed_df[closed_df['pnl_pct'] > 0])
            win_rate = (win_trades / closed_trades) * 100
            
            print(f"💰 總盈虧: {total_pnl:+.2f}%")
            print(f"🏆 勝率: {win_rate:.1f}%")
        
        print("\n" + "=" * 80)
        
        # 顯示活躍交易
        if active_trades > 0:
            print("\n💼 活躍交易:")
            active_df = df[df['status'] == 'ACTIVE']
            for _, trade in active_df.iterrows():
                entry_time = datetime.fromisoformat(trade['entry_time'])
                duration = datetime.now() - entry_time
                hours = int(duration.total_seconds() // 3600)
                minutes = int((duration.total_seconds() % 3600) // 60)
                
                print(f"  🔸 {trade['symbol']} {trade['timeframe']} {trade['direction']}")
                print(f"     入場: ${float(trade['entry_price']):.6f}")
                print(f"     止盈: ${float(trade['take_profit_price']):.6f}")
                print(f"     止損: ${float(trade['stop_loss_price']):.6f}")
                print(f"     時間: {hours}h{minutes}m")
                print()
        
        # 顯示最近完成的交易
        if closed_trades > 0:
            print("📈 最近完成的交易 (最多10個):")
            recent_df = df[df['status'] == 'CLOSED'].tail(10)
            for _, trade in recent_df.iterrows():
                pnl = float(trade['pnl_pct']) if trade['pnl_pct'] else 0
                pnl_emoji = "💰" if pnl > 0 else "💸"
                exit_emoji = "🎯" if trade['exit_type'] == 'TAKE_PROFIT' else "🛑"
                
                exit_time = datetime.fromisoformat(trade['exit_time'])
                
                print(f"  {exit_emoji} {trade['symbol']} {trade['timeframe']} {trade['direction']}")
                print(f"     {pnl_emoji} 盈虧: {pnl:+.2f}% | {trade['exit_type']}")
                print(f"     時間: {exit_time.strftime('%m-%d %H:%M')}")
                print()
        
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 讀取交易記錄失敗: {e}")

def export_daily_report():
    """導出今日交易報告"""
    csv_path = "data/trades_record.csv"
    
    if not os.path.exists(csv_path):
        print("❌ 交易記錄文件不存在")
        return
    
    try:
        df = pd.read_csv(csv_path)
        today = datetime.now().date()
        
        # 篩選今日交易
        today_trades = []
        for _, trade in df.iterrows():
            if trade['status'] == 'CLOSED' and trade['exit_time']:
                exit_date = datetime.fromisoformat(trade['exit_time']).date()
                if exit_date == today:
                    today_trades.append(trade)
        
        if not today_trades:
            print("📊 今日暫無完成交易")
            return
        
        print(f"📊 今日交易報告 ({today})")
        print("=" * 60)
        
        total_pnl = 0
        win_count = 0
        
        for trade in today_trades:
            pnl = float(trade['pnl_pct']) if trade['pnl_pct'] else 0
            total_pnl += pnl
            if pnl > 0:
                win_count += 1
            
            pnl_emoji = "💰" if pnl > 0 else "💸"
            exit_emoji = "🎯" if trade['exit_type'] == 'TAKE_PROFIT' else "🛑"
            
            print(f"{exit_emoji} {trade['symbol']} {trade['timeframe']} {trade['direction']}")
            print(f"   {pnl_emoji} {pnl:+.2f}% | {trade['exit_type']}")
        
        win_rate = (win_count / len(today_trades)) * 100
        print("\n" + "=" * 60)
        print(f"📈 今日統計:")
        print(f"   完成交易: {len(today_trades)}個")
        print(f"   總盈虧: {total_pnl:+.2f}%")
        print(f"   勝率: {win_rate:.1f}%")
        
    except Exception as e:
        print(f"❌ 生成今日報告失敗: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "today":
        export_daily_report()
    else:
        view_trades()
        print("\n💡 使用 'python view_trades.py today' 查看今日報告")
