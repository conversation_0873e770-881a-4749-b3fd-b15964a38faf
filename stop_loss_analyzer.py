#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
止盈止損方法分析器
用於測試和比較不同止盈止損方法的效果
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import asyncio
import aiohttp
import os
import sys

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stop_loss_take_profit_research import StopLossTakeProfitCalculator, StopLossResult, StopLossType
from src.data_fetcher import DataFetcher
from src.config_manager import ConfigManager

class StopLossAnalyzer:
    """止盈止損分析器"""
    
    def __init__(self):
        self.config = ConfigManager()
        self.data_fetcher = DataFetcher(self.config)
        self.calculator = StopLossTakeProfitCalculator()
        
        # 測試幣種
        self.test_symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', '1000PEPEUSDT']
        self.test_timeframes = ['1h', '4h']
        
    async def fetch_test_data(self, symbol: str, timeframe: str, days: int = 30) -> pd.DataFrame:
        """獲取測試數據"""
        try:
            # 計算開始時間
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)
            
            # 獲取K線數據
            df = await self.data_fetcher.get_bybit_data(symbol=symbol, timeframe=timeframe.upper())

            if df is None or df.empty:
                return None
            
            return df
            
        except Exception as e:
            print(f"獲取 {symbol} {timeframe} 數據失敗: {e}")
            return None
    
    def simulate_trade(self, df: pd.DataFrame, entry_idx: int, direction: str,
                      stop_loss_result: StopLossResult) -> Dict:
        """
        模擬交易執行
        
        Args:
            df: 價格數據
            entry_idx: 入場索引
            direction: 交易方向
            stop_loss_result: 止盈止損結果
            
        Returns:
            交易結果字典
        """
        entry_price = df['close'].iloc[entry_idx]
        stop_loss_price = stop_loss_result.stop_loss_price
        take_profit_price = stop_loss_result.take_profit_price
        
        # 從入場後開始檢查
        for i in range(entry_idx + 1, len(df)):
            current_high = df['high'].iloc[i]
            current_low = df['low'].iloc[i]
            current_close = df['close'].iloc[i]
            
            if direction == 'LONG':
                # 檢查止損
                if current_low <= stop_loss_price:
                    exit_price = stop_loss_price
                    pnl_pct = ((exit_price - entry_price) / entry_price) * 100
                    return {
                        'exit_type': 'STOP_LOSS',
                        'exit_price': exit_price,
                        'exit_idx': i,
                        'pnl_pct': pnl_pct,
                        'holding_bars': i - entry_idx,
                        'method': stop_loss_result.method.value
                    }
                
                # 檢查止盈
                if current_high >= take_profit_price:
                    exit_price = take_profit_price
                    pnl_pct = ((exit_price - entry_price) / entry_price) * 100
                    return {
                        'exit_type': 'TAKE_PROFIT',
                        'exit_price': exit_price,
                        'exit_idx': i,
                        'pnl_pct': pnl_pct,
                        'holding_bars': i - entry_idx,
                        'method': stop_loss_result.method.value
                    }
            
            else:  # SHORT
                # 檢查止損
                if current_high >= stop_loss_price:
                    exit_price = stop_loss_price
                    pnl_pct = ((entry_price - exit_price) / entry_price) * 100
                    return {
                        'exit_type': 'STOP_LOSS',
                        'exit_price': exit_price,
                        'exit_idx': i,
                        'pnl_pct': pnl_pct,
                        'holding_bars': i - entry_idx,
                        'method': stop_loss_result.method.value
                    }
                
                # 檢查止盈
                if current_low <= take_profit_price:
                    exit_price = take_profit_price
                    pnl_pct = ((entry_price - exit_price) / entry_price) * 100
                    return {
                        'exit_type': 'TAKE_PROFIT',
                        'exit_price': exit_price,
                        'exit_idx': i,
                        'pnl_pct': pnl_pct,
                        'holding_bars': i - entry_idx,
                        'method': stop_loss_result.method.value
                    }
        
        # 如果沒有觸發止盈止損，以最後價格平倉
        exit_price = df['close'].iloc[-1]
        if direction == 'LONG':
            pnl_pct = ((exit_price - entry_price) / entry_price) * 100
        else:
            pnl_pct = ((entry_price - exit_price) / entry_price) * 100
        
        return {
            'exit_type': 'TIMEOUT',
            'exit_price': exit_price,
            'exit_idx': len(df) - 1,
            'pnl_pct': pnl_pct,
            'holding_bars': len(df) - 1 - entry_idx,
            'method': stop_loss_result.method.value
        }
    
    def generate_random_signals(self, df: pd.DataFrame, num_signals: int = 50) -> List[Tuple[int, str]]:
        """
        生成隨機交易信號用於測試
        
        Args:
            df: 價格數據
            num_signals: 信號數量
            
        Returns:
            (索引, 方向) 的列表
        """
        signals = []
        
        # 確保有足夠的數據進行測試
        start_idx = 50  # 留出計算指標的空間
        end_idx = len(df) - 20  # 留出交易執行的空間
        
        for _ in range(num_signals):
            idx = np.random.randint(start_idx, end_idx)
            direction = np.random.choice(['LONG', 'SHORT'])
            signals.append((idx, direction))
        
        return signals
    
    async def test_stop_loss_methods(self, symbol: str, timeframe: str) -> pd.DataFrame:
        """
        測試所有止盈止損方法
        
        Args:
            symbol: 交易對
            timeframe: 時間框架
            
        Returns:
            測試結果DataFrame
        """
        print(f"🧪 測試 {symbol} {timeframe} 的止盈止損方法...")
        
        # 獲取數據
        df = await self.fetch_test_data(symbol, timeframe)
        if df is None or len(df) < 100:
            print(f"❌ {symbol} {timeframe} 數據不足")
            return None
        
        # 生成測試信號
        signals = self.generate_random_signals(df, num_signals=30)
        
        results = []
        
        for entry_idx, direction in signals:
            entry_price = df['close'].iloc[entry_idx]
            
            # 獲取該點的數據切片（用於計算指標）
            data_slice = df.iloc[:entry_idx+1].copy()
            
            # 獲取所有止盈止損方法
            methods = self.calculator.get_all_stop_loss_methods(
                data_slice, direction, entry_price
            )
            
            # 測試每種方法
            for method_result in methods:
                trade_result = self.simulate_trade(
                    df, entry_idx, direction, method_result
                )
                
                # 合併結果
                result = {
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'entry_idx': entry_idx,
                    'direction': direction,
                    'entry_price': entry_price,
                    'stop_loss_price': method_result.stop_loss_price,
                    'take_profit_price': method_result.take_profit_price,
                    'method': method_result.method.value,
                    'confidence': method_result.confidence,
                    'risk_reward_ratio': method_result.risk_reward_ratio,
                    **trade_result
                }
                
                results.append(result)
        
        return pd.DataFrame(results)
    
    def analyze_results(self, results_df: pd.DataFrame) -> Dict:
        """
        分析測試結果
        
        Args:
            results_df: 測試結果DataFrame
            
        Returns:
            分析結果字典
        """
        if results_df.empty:
            return {}
        
        analysis = {}
        
        # 按方法分組分析
        for method in results_df['method'].unique():
            method_data = results_df[results_df['method'] == method]
            
            # 基本統計
            total_trades = len(method_data)
            winning_trades = len(method_data[method_data['pnl_pct'] > 0])
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # 盈虧統計
            avg_pnl = method_data['pnl_pct'].mean()
            avg_win = method_data[method_data['pnl_pct'] > 0]['pnl_pct'].mean()
            avg_loss = method_data[method_data['pnl_pct'] < 0]['pnl_pct'].mean()
            
            # 風險指標
            max_drawdown = method_data['pnl_pct'].min()
            volatility = method_data['pnl_pct'].std()
            
            # 持倉時間
            avg_holding_bars = method_data['holding_bars'].mean()
            
            # 止盈止損比例
            tp_rate = len(method_data[method_data['exit_type'] == 'TAKE_PROFIT']) / total_trades
            sl_rate = len(method_data[method_data['exit_type'] == 'STOP_LOSS']) / total_trades
            
            analysis[method] = {
                'total_trades': total_trades,
                'win_rate': win_rate,
                'avg_pnl': avg_pnl,
                'avg_win': avg_win if not pd.isna(avg_win) else 0,
                'avg_loss': avg_loss if not pd.isna(avg_loss) else 0,
                'max_drawdown': max_drawdown,
                'volatility': volatility,
                'avg_holding_bars': avg_holding_bars,
                'take_profit_rate': tp_rate,
                'stop_loss_rate': sl_rate,
                'avg_confidence': method_data['confidence'].mean(),
                'avg_risk_reward': method_data['risk_reward_ratio'].mean()
            }
        
        return analysis
