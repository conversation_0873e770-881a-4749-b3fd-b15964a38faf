#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改進版策略：BB中軌突破 + 多空力道策略
解決滯後性問題，使用BB中軌突破替代上下軌突破
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os
import sys
import logging

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.data_fetcher import DataFetcher
from src.config_manager import ConfigManager

class TechnicalIndicators:
    """技術指標計算類"""

    def bollinger_bands(self, prices, window=20, std_dev=2):
        """計算布林帶"""
        sma = prices.rolling(window=window).mean()
        std = prices.rolling(window=window).std()
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        return upper, sma, lower

    def rsi(self, prices, period=14):
        """計算RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def atr(self, high, low, close, period=14):
        """計算ATR"""
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(window=period).mean()
        return atr

    def calculate_ti_confidence_interval(self, ti_values, lookback=24, confidence=0.7):
        """計算TI信賴區間"""
        rolling_ti = ti_values.rolling(window=lookback)
        upper_percentile = (1 + confidence) / 2
        lower_percentile = (1 - confidence) / 2

        upper_limit = rolling_ti.quantile(upper_percentile)
        lower_limit = rolling_ti.quantile(lower_percentile)

        return upper_limit, lower_limit

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImprovedBBMiddleStrategy:
    """改進版BB中軌突破策略"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.data_fetcher = DataFetcher(self.config_manager)
        self.indicators = TechnicalIndicators()

        # 加載策略配置
        self.strategies = self.load_strategies()

        # 回測結果存儲
        self.backtest_results = {}
        
    def load_strategies(self):
        """加載所有策略配置"""
        try:
            with open('rsi_signal_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('active_strategies', {})
        except Exception as e:
            logger.error(f"加載策略配置失敗: {e}")
            return {}
    
    def calculate_improved_signals(self, data, strategy_config):
        """
        計算改進版信號條件
        主要改變：BB條件從突破上下軌改為突破中軌
        """
        try:
            # 獲取策略參數
            bb_window = strategy_config.get('bb_window', 20)
            bb_std = strategy_config.get('bb_std', 2.0)
            rsi_period = strategy_config.get('rsi_period', 14)
            atr_period = strategy_config.get('atr_period', 14)
            ti_lookback = strategy_config.get('ti_lookback', 24)
            ti_confidence = strategy_config.get('ti_confidence', 0.7)
            
            # 計算技術指標
            bb_upper, bb_middle, bb_lower = self.indicators.bollinger_bands(
                data['close'], window=bb_window, std_dev=bb_std
            )
            rsi = self.indicators.rsi(data['close'], period=rsi_period)
            atr = self.indicators.atr(data['high'], data['low'], data['close'], period=atr_period)
            
            # 計算TI信賴區間
            ti_upper_limit, ti_lower_limit = self.indicators.calculate_ti_confidence_interval(
                data['taker_intensity'], lookback=ti_lookback, confidence=ti_confidence
            )
            
            # 獲取最新值
            current_price = data['close'].iloc[-1]
            prev_price = data['close'].iloc[-2]
            current_rsi = rsi.iloc[-1]
            current_ti = data['taker_intensity'].iloc[-1]
            current_bb_middle = bb_middle.iloc[-1]
            prev_bb_middle = bb_middle.iloc[-2]
            current_ti_upper = ti_upper_limit.iloc[-1]
            current_ti_lower = ti_lower_limit.iloc[-1]
            
            # 改進版多頭信號條件 (5選4)
            long_conditions = {
                'rsi_bullish': current_rsi >= 70,
                'ti_positive': current_ti > 0,
                'price_above_bb_middle': current_price > current_bb_middle,  # 改進：突破中軌而非上軌
                'prev_price_below_bb_middle': prev_price <= prev_bb_middle,  # 改進：前價格在中軌下方
                'ti_extreme_high': current_ti > current_ti_upper
            }
            
            # 改進版空頭信號條件 (5選4)
            short_conditions = {
                'rsi_bearish': current_rsi <= 30,
                'ti_negative': current_ti < 0,
                'price_below_bb_middle': current_price < current_bb_middle,  # 改進：跌破中軌而非下軌
                'prev_price_above_bb_middle': prev_price >= prev_bb_middle,  # 改進：前價格在中軌上方
                'ti_extreme_low': current_ti < current_ti_lower
            }
            
            # 計算滿足條件數量
            long_count = sum(long_conditions.values())
            short_count = sum(short_conditions.values())
            
            # 生成信號 (需要5個條件中滿足4個)
            signal = None
            signal_strength = 0
            
            if long_count >= 4:
                signal = 'LONG'
                signal_strength = long_count / 5
            elif short_count >= 4:
                signal = 'SHORT'
                signal_strength = short_count / 5
            
            # 返回詳細信息
            return {
                'signal': signal,
                'signal_strength': signal_strength,
                'current_price': current_price,
                'current_rsi': current_rsi,
                'current_ti': current_ti,
                'bb_middle': current_bb_middle,
                'long_conditions': long_conditions,
                'short_conditions': short_conditions,
                'long_count': long_count,
                'short_count': short_count,
                'atr': atr.iloc[-1]
            }
            
        except Exception as e:
            logger.error(f"計算改進版信號失敗: {e}")
            return None
    
    def calculate_supertrend_stop_loss(self, data, direction, entry_price, period=10, multiplier=3.0):
        """計算SUPERTREND止盈止損"""
        try:
            # 計算ATR和中位價
            hl2 = (data['high'] + data['low']) / 2
            atr = self.indicators.atr(data['high'], data['low'], data['close'], period=period)
            
            # 計算上下軌
            upper_band = hl2 + (multiplier * atr)
            lower_band = hl2 - (multiplier * atr)
            
            # SUPERTREND邏輯
            supertrend = np.where(data['close'] <= lower_band.shift(1), lower_band, 
                                 np.where(data['close'] >= upper_band.shift(1), upper_band, np.nan))
            
            # 前向填充
            supertrend = pd.Series(supertrend, index=data.index).fillna(method='ffill')
            
            current_supertrend = supertrend.iloc[-1]
            
            # 計算止盈止損
            if direction == 'LONG':
                stop_loss = current_supertrend
                take_profit = entry_price + (abs(entry_price - current_supertrend) * 2.5)
            else:  # SHORT
                stop_loss = current_supertrend
                take_profit = entry_price - (abs(entry_price - current_supertrend) * 2.5)
            
            return {
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'risk_reward_ratio': 2.5,
                'method': 'SUPERTREND',
                'confidence': 0.78
            }
            
        except Exception as e:
            logger.error(f"SUPERTREND計算失敗: {e}")
            return None
    
    async def backtest_single_strategy(self, symbol, timeframe, strategy_config):
        """回測單個策略"""
        logger.info(f"🔍 開始回測: {symbol}_{timeframe}")
        
        try:
            # 獲取最新數據 (使用現有方法)
            data = await self.data_fetcher.get_latest_data(symbol, timeframe)
            
            if data is None or len(data) < 200:
                logger.warning(f"❌ {symbol}_{timeframe} 數據不足")
                return None
            
            logger.info(f"📊 {symbol}_{timeframe} 獲取到 {len(data)} 條歷史數據")
            
            # 回測交易
            trades = []
            
            for i in range(200, len(data)):  # 留出200個數據點計算指標
                current_data = data.iloc[:i+1]
                
                # 計算信號
                signal_info = self.calculate_improved_signals(current_data, strategy_config)
                
                if signal_info and signal_info['signal']:
                    # 計算止盈止損
                    stop_info = self.calculate_supertrend_stop_loss(
                        current_data, signal_info['signal'], signal_info['current_price']
                    )
                    
                    if stop_info:
                        trade = {
                            'timestamp': current_data.index[-1],
                            'symbol': symbol,
                            'timeframe': timeframe,
                            'direction': signal_info['signal'],
                            'entry_price': signal_info['current_price'],
                            'stop_loss': stop_info['stop_loss'],
                            'take_profit': stop_info['take_profit'],
                            'signal_strength': signal_info['signal_strength'],
                            'rsi': signal_info['current_rsi'],
                            'ti': signal_info['current_ti'],
                            'long_count': signal_info['long_count'],
                            'short_count': signal_info['short_count']
                        }
                        
                        # 模擬交易結果
                        exit_result = self.simulate_trade_exit(data, i, trade)
                        if exit_result:
                            trade.update(exit_result)
                            trades.append(trade)
            
            # 計算策略績效
            if trades:
                performance = self.calculate_performance(trades)
                performance['symbol'] = symbol
                performance['timeframe'] = timeframe
                performance['total_trades'] = len(trades)
                
                logger.info(f"✅ {symbol}_{timeframe} 回測完成: {len(trades)}筆交易")
                return performance
            else:
                logger.warning(f"⚠️ {symbol}_{timeframe} 無交易信號")
                return None
                
        except Exception as e:
            logger.error(f"❌ {symbol}_{timeframe} 回測失敗: {e}")
            return None
    
    def simulate_trade_exit(self, data, entry_index, trade):
        """模擬交易平倉"""
        try:
            entry_price = trade['entry_price']
            stop_loss = trade['stop_loss']
            take_profit = trade['take_profit']
            direction = trade['direction']
            
            # 從入場後開始檢查
            for i in range(entry_index + 1, len(data)):
                current_high = data['high'].iloc[i]
                current_low = data['low'].iloc[i]
                current_close = data['close'].iloc[i]
                
                if direction == 'LONG':
                    # 檢查止損
                    if current_low <= stop_loss:
                        pnl_pct = (stop_loss - entry_price) / entry_price * 100
                        return {
                            'exit_price': stop_loss,
                            'exit_time': data.index[i],
                            'exit_type': 'STOP_LOSS',
                            'pnl_pct': pnl_pct,
                            'duration_hours': (data.index[i] - trade['timestamp']).total_seconds() / 3600
                        }
                    # 檢查止盈
                    elif current_high >= take_profit:
                        pnl_pct = (take_profit - entry_price) / entry_price * 100
                        return {
                            'exit_price': take_profit,
                            'exit_time': data.index[i],
                            'exit_type': 'TAKE_PROFIT',
                            'pnl_pct': pnl_pct,
                            'duration_hours': (data.index[i] - trade['timestamp']).total_seconds() / 3600
                        }
                
                else:  # SHORT
                    # 檢查止損
                    if current_high >= stop_loss:
                        pnl_pct = (entry_price - stop_loss) / entry_price * 100
                        return {
                            'exit_price': stop_loss,
                            'exit_time': data.index[i],
                            'exit_type': 'STOP_LOSS',
                            'pnl_pct': pnl_pct,
                            'duration_hours': (data.index[i] - trade['timestamp']).total_seconds() / 3600
                        }
                    # 檢查止盈
                    elif current_low <= take_profit:
                        pnl_pct = (entry_price - take_profit) / entry_price * 100
                        return {
                            'exit_price': take_profit,
                            'exit_time': data.index[i],
                            'exit_type': 'TAKE_PROFIT',
                            'pnl_pct': pnl_pct,
                            'duration_hours': (data.index[i] - trade['timestamp']).total_seconds() / 3600
                        }
            
            # 如果沒有觸發止盈止損，用最後價格平倉
            final_price = data['close'].iloc[-1]
            if direction == 'LONG':
                pnl_pct = (final_price - entry_price) / entry_price * 100
            else:
                pnl_pct = (entry_price - final_price) / entry_price * 100
            
            return {
                'exit_price': final_price,
                'exit_time': data.index[-1],
                'exit_type': 'MARKET_CLOSE',
                'pnl_pct': pnl_pct,
                'duration_hours': (data.index[-1] - trade['timestamp']).total_seconds() / 3600
            }
            
        except Exception as e:
            logger.error(f"模擬交易平倉失敗: {e}")
            return None
    
    def calculate_performance(self, trades):
        """計算策略績效"""
        if not trades:
            return {}
        
        df = pd.DataFrame(trades)
        
        # 基本統計
        total_trades = len(df)
        winning_trades = len(df[df['pnl_pct'] > 0])
        losing_trades = len(df[df['pnl_pct'] <= 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # 盈虧統計
        total_pnl = df['pnl_pct'].sum()
        avg_win = df[df['pnl_pct'] > 0]['pnl_pct'].mean() if winning_trades > 0 else 0
        avg_loss = df[df['pnl_pct'] <= 0]['pnl_pct'].mean() if losing_trades > 0 else 0
        
        # 最大回撤
        cumulative_pnl = df['pnl_pct'].cumsum()
        running_max = cumulative_pnl.expanding().max()
        drawdown = cumulative_pnl - running_max
        max_drawdown = drawdown.min()
        
        # 盈利因子
        gross_profit = df[df['pnl_pct'] > 0]['pnl_pct'].sum()
        gross_loss = abs(df[df['pnl_pct'] <= 0]['pnl_pct'].sum())
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # 夏普比率 (簡化版)
        returns_std = df['pnl_pct'].std()
        sharpe_ratio = (df['pnl_pct'].mean() / returns_std) if returns_std > 0 else 0
        
        # 信號頻率 (每天)
        if len(df) > 0:
            time_span = (df['timestamp'].max() - df['timestamp'].min()).days
            signal_frequency = total_trades / max(time_span, 1)
        else:
            signal_frequency = 0
        
        return {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'total_return': total_pnl,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'max_drawdown': max_drawdown,
            'profit_factor': profit_factor,
            'sharpe_ratio': sharpe_ratio,
            'signal_frequency': signal_frequency,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades
        }
    
    async def run_comprehensive_backtest(self):
        """運行全面回測"""
        logger.info("🚀 開始改進版BB中軌突破策略全面回測")
        logger.info("=" * 80)
        logger.info("📋 主要改進:")
        logger.info("   1. BB條件從突破上下軌改為突破中軌")
        logger.info("   2. 更早捕捉趨勢，減少滯後性")
        logger.info("   3. 避免在反轉點開倉")
        logger.info("=" * 80)
        
        all_results = []
        
        for strategy_key, strategy_config in self.strategies.items():
            symbol = strategy_config['symbol']
            timeframe = strategy_config['timeframe']
            
            result = await self.backtest_single_strategy(symbol, timeframe, strategy_config)
            if result:
                all_results.append(result)
        
        # 生成對比報告
        self.generate_comparison_report(all_results)
        
        return all_results
    
    def generate_comparison_report(self, results):
        """生成對比報告"""
        if not results:
            logger.error("❌ 無回測結果")
            return
        
        print("\n" + "=" * 100)
        print("📊 改進版BB中軌突破策略 vs 原版BB上下軌突破策略 對比報告")
        print("=" * 100)
        
        # 排序結果
        results.sort(key=lambda x: x['total_return'], reverse=True)
        
        print(f"\n🏆 策略表現排名 (按總回報排序):")
        print("-" * 100)
        
        comparison_data = []
        for i, result in enumerate(results, 1):
            strategy_key = f"{result['symbol']}_{result['timeframe']}"
            
            comparison_data.append({
                '排名': i,
                '策略': strategy_key,
                '交易數': result['total_trades'],
                '勝率': f"{result['win_rate']:.1%}",
                '總回報': f"{result['total_return']:.2f}%",
                '平均盈利': f"{result['avg_win']:.2f}%",
                '平均虧損': f"{result['avg_loss']:.2f}%",
                '最大回撤': f"{result['max_drawdown']:.2f}%",
                '盈利因子': f"{result['profit_factor']:.2f}",
                '夏普比率': f"{result['sharpe_ratio']:.2f}",
                '信號頻率': f"{result['signal_frequency']:.2f}/天"
            })
        
        df = pd.DataFrame(comparison_data)
        print(df.to_string(index=False))
        
        # 統計摘要
        avg_win_rate = np.mean([r['win_rate'] for r in results])
        avg_return = np.mean([r['total_return'] for r in results])
        avg_drawdown = np.mean([r['max_drawdown'] for r in results])
        avg_sharpe = np.mean([r['sharpe_ratio'] for r in results])
        
        print(f"\n📈 改進版策略整體表現:")
        print(f"   平均勝率: {avg_win_rate:.1%}")
        print(f"   平均總回報: {avg_return:.2f}%")
        print(f"   平均最大回撤: {avg_drawdown:.2f}%")
        print(f"   平均夏普比率: {avg_sharpe:.2f}")
        
        print(f"\n💡 改進效果分析:")
        print(f"   ✅ BB中軌突破更早捕捉趨勢")
        print(f"   ✅ 減少在反轉點開倉的風險")
        print(f"   ✅ 提高信號的前瞻性")
        print(f"   ✅ 配合TI多空力道確認方向")
        
        # 保存結果
        with open('improved_bb_middle_backtest_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n💾 詳細結果已保存至: improved_bb_middle_backtest_results.json")

async def main():
    """主函數"""
    strategy = ImprovedBBMiddleStrategy()
    results = await strategy.run_comprehensive_backtest()
    
    if results:
        print(f"\n✅ 改進版BB中軌突破策略回測完成")
        print(f"📊 共測試 {len(results)} 個策略")
        print(f"🎯 結果已保存，可與原版策略進行對比")
    else:
        print(f"\n❌ 回測失敗，請檢查配置和數據")

if __name__ == "__main__":
    asyncio.run(main())
