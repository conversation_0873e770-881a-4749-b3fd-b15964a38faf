#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
止盈止損方法演示程序
使用模擬數據展示不同止盈止損方法的效果
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from stop_loss_take_profit_research import StopLossTakeProfitCalculator, StopLossType

class StopLossMethodDemo:
    """止盈止損方法演示類"""
    
    def __init__(self):
        self.calculator = StopLossTakeProfitCalculator()
        
    def generate_sample_data(self, symbol: str = "BTCUSDT", days: int = 30) -> pd.DataFrame:
        """生成模擬價格數據"""
        np.random.seed(42)  # 確保結果可重現
        
        # 生成時間序列
        start_date = datetime.now() - timedelta(days=days)
        dates = pd.date_range(start=start_date, periods=days*24, freq='H')
        
        # 生成價格數據（隨機遊走 + 趨勢）
        base_price = 100000 if symbol == "BTCUSDT" else 4000
        returns = np.random.normal(0, 0.02, len(dates))  # 2%波動率
        
        # 添加一些趨勢和週期性
        trend = np.linspace(0, 0.1, len(dates))  # 10%上升趨勢
        cycle = 0.05 * np.sin(np.arange(len(dates)) * 2 * np.pi / (24*7))  # 週期性波動
        
        price_changes = returns + trend/len(dates) + cycle/len(dates)
        prices = base_price * np.exp(np.cumsum(price_changes))
        
        # 生成OHLC數據
        df = pd.DataFrame(index=dates)
        df['close'] = prices
        
        # 生成開高低價（基於收盤價的合理範圍）
        noise = np.random.normal(0, 0.005, len(dates))  # 0.5%噪音
        df['open'] = df['close'].shift(1).fillna(df['close'].iloc[0]) * (1 + noise)
        df['high'] = np.maximum(df['open'], df['close']) * (1 + np.abs(np.random.normal(0, 0.01, len(dates))))
        df['low'] = np.minimum(df['open'], df['close']) * (1 - np.abs(np.random.normal(0, 0.01, len(dates))))
        
        # 生成成交量
        df['volume'] = np.random.lognormal(10, 0.5, len(dates))
        
        return df
    
    def demonstrate_all_methods(self, symbol: str = "BTCUSDT"):
        """演示所有止盈止損方法"""
        print(f"🎯 {symbol} 止盈止損方法演示")
        print("=" * 60)
        
        # 生成測試數據
        df = self.generate_sample_data(symbol)
        
        # 選擇一個測試點（數據中間位置）
        test_idx = len(df) // 2
        entry_price = df['close'].iloc[test_idx]
        test_data = df.iloc[:test_idx+1].copy()
        
        print(f"📊 測試數據: {len(test_data)} 個小時K線")
        print(f"💰 入場價格: ${entry_price:.2f}")
        print(f"📅 入場時間: {test_data.index[-1]}")
        
        # 測試LONG和SHORT兩個方向
        for direction in ['LONG', 'SHORT']:
            print(f"\n{'='*20} {direction} 方向 {'='*20}")
            
            # 獲取所有方法的結果
            methods = self.calculator.get_all_stop_loss_methods(test_data, direction, entry_price)
            
            if not methods:
                print(f"❌ 無法計算 {direction} 方向的止盈止損")
                continue
            
            # 顯示每種方法的結果
            results_data = []
            for method in methods:
                risk = abs(entry_price - method.stop_loss_price)
                reward = abs(method.take_profit_price - entry_price)
                
                results_data.append({
                    '方法': method.method.value,
                    '止損價格': f"${method.stop_loss_price:.2f}",
                    '止盈價格': f"${method.take_profit_price:.2f}",
                    '風險': f"${risk:.2f} ({risk/entry_price*100:.1f}%)",
                    '回報': f"${reward:.2f} ({reward/entry_price*100:.1f}%)",
                    '風險回報比': f"{method.risk_reward_ratio:.2f}",
                    '信心度': f"{method.confidence:.2f}",
                    '原因': method.reason[:50] + "..." if len(method.reason) > 50 else method.reason
                })
            
            # 創建結果表格
            results_df = pd.DataFrame(results_data)
            print(results_df.to_string(index=False))
            
            # 推薦最佳方法
            best_method = self.calculator.recommend_best_method(methods, preference="balanced")
            if best_method:
                print(f"\n🏆 推薦方法: {best_method.method.value}")
                print(f"   風險回報比: {best_method.risk_reward_ratio:.2f}")
                print(f"   信心度: {best_method.confidence:.2f}")
                print(f"   原因: {best_method.reason}")
    
    def compare_methods_performance(self, symbol: str = "BTCUSDT", num_tests: int = 100):
        """比較不同方法的性能"""
        print(f"\n🔬 {symbol} 方法性能比較 ({num_tests} 次測試)")
        print("=" * 60)
        
        # 生成測試數據
        df = self.generate_sample_data(symbol, days=60)  # 更多數據用於測試
        
        results = []
        
        for test_num in range(num_tests):
            # 隨機選擇測試點
            test_idx = np.random.randint(50, len(df) - 50)  # 留出足夠的數據計算指標
            entry_price = df['close'].iloc[test_idx]
            test_data = df.iloc[:test_idx+1].copy()
            direction = np.random.choice(['LONG', 'SHORT'])
            
            # 獲取所有方法
            methods = self.calculator.get_all_stop_loss_methods(test_data, direction, entry_price)
            
            if not methods:
                continue
            
            # 模擬未來價格走勢（簡化）
            future_data = df.iloc[test_idx:test_idx+24].copy()  # 未來24小時
            if len(future_data) < 2:
                continue
            
            # 對每種方法進行測試
            for method in methods:
                result = self.simulate_trade_outcome(
                    future_data, entry_price, direction, method
                )
                
                if result:
                    results.append({
                        'method': method.method.value,
                        'direction': direction,
                        'entry_price': entry_price,
                        'risk_reward_ratio': method.risk_reward_ratio,
                        'confidence': method.confidence,
                        **result
                    })
        
        if not results:
            print("❌ 沒有足夠的測試結果")
            return
        
        # 分析結果
        results_df = pd.DataFrame(results)
        self.analyze_performance_results(results_df)
    
    def simulate_trade_outcome(self, future_data: pd.DataFrame, entry_price: float, 
                              direction: str, method) -> dict:
        """模擬交易結果"""
        stop_loss_price = method.stop_loss_price
        take_profit_price = method.take_profit_price
        
        for i, (timestamp, row) in enumerate(future_data.iterrows()):
            if direction == 'LONG':
                # 檢查止損
                if row['low'] <= stop_loss_price:
                    pnl_pct = ((stop_loss_price - entry_price) / entry_price) * 100
                    return {
                        'outcome': 'STOP_LOSS',
                        'exit_price': stop_loss_price,
                        'pnl_pct': pnl_pct,
                        'holding_hours': i + 1
                    }
                
                # 檢查止盈
                if row['high'] >= take_profit_price:
                    pnl_pct = ((take_profit_price - entry_price) / entry_price) * 100
                    return {
                        'outcome': 'TAKE_PROFIT',
                        'exit_price': take_profit_price,
                        'pnl_pct': pnl_pct,
                        'holding_hours': i + 1
                    }
            
            else:  # SHORT
                # 檢查止損
                if row['high'] >= stop_loss_price:
                    pnl_pct = ((entry_price - stop_loss_price) / entry_price) * 100
                    return {
                        'outcome': 'STOP_LOSS',
                        'exit_price': stop_loss_price,
                        'pnl_pct': pnl_pct,
                        'holding_hours': i + 1
                    }
                
                # 檢查止盈
                if row['low'] <= take_profit_price:
                    pnl_pct = ((entry_price - take_profit_price) / entry_price) * 100
                    return {
                        'outcome': 'TAKE_PROFIT',
                        'exit_price': take_profit_price,
                        'pnl_pct': pnl_pct,
                        'holding_hours': i + 1
                    }
        
        # 如果沒有觸發，以最後價格結算
        final_price = future_data['close'].iloc[-1]
        if direction == 'LONG':
            pnl_pct = ((final_price - entry_price) / entry_price) * 100
        else:
            pnl_pct = ((entry_price - final_price) / entry_price) * 100
        
        return {
            'outcome': 'TIMEOUT',
            'exit_price': final_price,
            'pnl_pct': pnl_pct,
            'holding_hours': len(future_data)
        }
    
    def analyze_performance_results(self, results_df: pd.DataFrame):
        """分析性能結果"""
        print("\n📊 性能分析結果:")
        print("-" * 60)
        
        # 按方法分組統計
        method_stats = []
        for method in results_df['method'].unique():
            method_data = results_df[results_df['method'] == method]
            
            total_trades = len(method_data)
            winning_trades = len(method_data[method_data['pnl_pct'] > 0])
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            avg_pnl = method_data['pnl_pct'].mean()
            avg_win = method_data[method_data['pnl_pct'] > 0]['pnl_pct'].mean()
            avg_loss = method_data[method_data['pnl_pct'] < 0]['pnl_pct'].mean()
            
            tp_rate = len(method_data[method_data['outcome'] == 'TAKE_PROFIT']) / total_trades
            
            method_stats.append({
                '方法': method,
                '交易數': total_trades,
                '勝率': f"{win_rate:.1%}",
                '平均盈虧': f"{avg_pnl:.2f}%",
                '平均盈利': f"{avg_win:.2f}%" if not pd.isna(avg_win) else "N/A",
                '平均虧損': f"{avg_loss:.2f}%" if not pd.isna(avg_loss) else "N/A",
                '止盈率': f"{tp_rate:.1%}"
            })
        
        stats_df = pd.DataFrame(method_stats)
        print(stats_df.to_string(index=False))
        
        # 找出最佳方法
        best_winrate = stats_df.loc[stats_df['勝率'].str.rstrip('%').astype(float).idxmax()]
        best_pnl = stats_df.loc[stats_df['平均盈虧'].str.rstrip('%').astype(float).idxmax()]
        
        print(f"\n🏆 最高勝率: {best_winrate['方法']} ({best_winrate['勝率']})")
        print(f"💰 最高平均盈虧: {best_pnl['方法']} ({best_pnl['平均盈虧']})")

def main():
    """主函數"""
    print("🎯 止盈止損方法演示程序")
    print("=" * 60)
    
    demo = StopLossMethodDemo()
    
    # 演示所有方法
    demo.demonstrate_all_methods("BTCUSDT")
    
    # 性能比較
    demo.compare_methods_performance("BTCUSDT", num_tests=50)
    
    print("\n✅ 演示完成！")

if __name__ == "__main__":
    main()
