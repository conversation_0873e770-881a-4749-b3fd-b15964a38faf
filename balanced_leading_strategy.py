#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
平衡版領先策略：結合領先因子和動能確認
解決滯後性問題的同時保持信號質量
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os
import sys
import logging

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.data_fetcher import DataFetcher
from src.config_manager import ConfigManager

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TechnicalIndicators:
    """技術指標計算類"""
    
    def bollinger_bands(self, prices, window=20, std_dev=2):
        """計算布林帶"""
        sma = prices.rolling(window=window).mean()
        std = prices.rolling(window=window).std()
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        return upper, sma, lower
    
    def rsi(self, prices, period=14):
        """計算RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def atr(self, high, low, close, period=14):
        """計算ATR"""
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(window=period).mean()
        return atr
    
    def calculate_ti_confidence_interval(self, ti_values, lookback=24, confidence=0.7):
        """計算TI信賴區間"""
        rolling_ti = ti_values.rolling(window=lookback)
        upper_percentile = (1 + confidence) / 2
        lower_percentile = (1 - confidence) / 2
        
        upper_limit = rolling_ti.quantile(upper_percentile)
        lower_limit = rolling_ti.quantile(lower_percentile)
        
        return upper_limit, lower_limit

class BalancedLeadingStrategy:
    """平衡版領先策略"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.data_fetcher = DataFetcher(self.config_manager)
        self.indicators = TechnicalIndicators()
        
        # 加載策略配置
        self.strategies = self.load_strategies()
        
    def load_strategies(self):
        """加載所有策略配置"""
        try:
            with open('rsi_signal_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('active_strategies', {})
        except Exception as e:
            logger.error(f"加載策略配置失敗: {e}")
            return {}
    
    def calculate_balanced_signals(self, data, strategy_config):
        """
        計算平衡版信號條件
        核心改進：
        1. 使用BB中軌作為領先指標
        2. 結合TI動能確認
        3. 添加RSI過濾避免極端區域
        4. 價格動能確認避免假突破
        """
        try:
            # 獲取策略參數
            bb_window = strategy_config.get('bb_window', 20)
            bb_std = strategy_config.get('bb_std', 2.0)
            rsi_period = strategy_config.get('rsi_period', 14)
            atr_period = strategy_config.get('atr_period', 14)
            ti_lookback = strategy_config.get('ti_lookback', 24)
            ti_confidence = strategy_config.get('ti_confidence', 0.7)
            
            # 計算技術指標
            bb_upper, bb_middle, bb_lower = self.indicators.bollinger_bands(
                data['Close'], window=bb_window, std_dev=bb_std
            )
            rsi = self.indicators.rsi(data['Close'], period=rsi_period)
            atr = self.indicators.atr(data['High'], data['Low'], data['Close'], period=atr_period)
            
            # 計算TI信賴區間 (使用合併後的TI數據)
            ti_data = data['long_taker_intensity'] - data['short_taker_intensity']  # 計算淨多空力道
            ti_upper_limit, ti_lower_limit = self.indicators.calculate_ti_confidence_interval(
                ti_data, lookback=ti_lookback, confidence=ti_confidence
            )

            # 獲取最新值
            current_price = data['Close'].iloc[-1]
            prev_price = data['Close'].iloc[-2]
            prev2_price = data['Close'].iloc[-3]

            current_rsi = rsi.iloc[-1]
            current_ti = ti_data.iloc[-1]
            prev_ti = ti_data.iloc[-2]
            
            current_bb_upper = bb_upper.iloc[-1]
            current_bb_middle = bb_middle.iloc[-1]
            current_bb_lower = bb_lower.iloc[-1]
            prev_bb_middle = bb_middle.iloc[-2]
            
            current_ti_upper = ti_upper_limit.iloc[-1]
            current_ti_lower = ti_lower_limit.iloc[-1]
            
            # 計算價格動能
            price_momentum = (current_price - prev2_price) / prev2_price * 100
            
            # 平衡版多頭信號條件 (5選4，但更嚴格的組合)
            long_conditions = {
                # 領先指標：價格接近或突破BB中軌上方
                'price_near_bb_middle_up': current_price > current_bb_middle and prev_price <= prev_bb_middle,
                
                # 動能確認：TI為正且增強
                'ti_positive_momentum': current_ti > 0 and current_ti > prev_ti,
                
                # 避免極端區域：RSI不在超買區
                'rsi_not_overbought': current_rsi < 75,  # 放寬到75避免錯過機會
                
                # 價格動能確認：有向上動能
                'price_momentum_up': price_momentum > 0.5,  # 至少0.5%的向上動能
                
                # TI極值確認：TI超過信賴區間上限
                'ti_extreme_high': current_ti > current_ti_upper
            }
            
            # 平衡版空頭信號條件 (5選4，但更嚴格的組合)
            short_conditions = {
                # 領先指標：價格接近或跌破BB中軌下方
                'price_near_bb_middle_down': current_price < current_bb_middle and prev_price >= prev_bb_middle,
                
                # 動能確認：TI為負且減弱
                'ti_negative_momentum': current_ti < 0 and current_ti < prev_ti,
                
                # 避免極端區域：RSI不在超賣區
                'rsi_not_oversold': current_rsi > 25,  # 放寬到25避免錯過機會
                
                # 價格動能確認：有向下動能
                'price_momentum_down': price_momentum < -0.5,  # 至少-0.5%的向下動能
                
                # TI極值確認：TI低於信賴區間下限
                'ti_extreme_low': current_ti < current_ti_lower
            }
            
            # 計算滿足條件數量
            long_count = sum(long_conditions.values())
            short_count = sum(short_conditions.values())
            
            # 生成信號 (需要5個條件中滿足4個)
            signal = None
            signal_strength = 0
            
            if long_count >= 4:
                signal = 'LONG'
                signal_strength = long_count / 5
            elif short_count >= 4:
                signal = 'SHORT'
                signal_strength = short_count / 5
            
            # 返回詳細信息
            return {
                'signal': signal,
                'signal_strength': signal_strength,
                'current_price': current_price,
                'current_rsi': current_rsi,
                'current_ti': current_ti,
                'bb_upper': current_bb_upper,
                'bb_middle': current_bb_middle,
                'bb_lower': current_bb_lower,
                'price_momentum': price_momentum,
                'long_conditions': long_conditions,
                'short_conditions': short_conditions,
                'long_count': long_count,
                'short_count': short_count,
                'atr': atr.iloc[-1]
            }
            
        except Exception as e:
            logger.error(f"計算平衡版信號失敗: {e}")
            return None
    
    async def test_single_strategy(self, symbol, timeframe, strategy_config):
        """測試單個策略的信號生成"""
        logger.info(f"🔍 測試策略: {symbol}_{timeframe}")
        
        try:
            # 獲取最新數據
            data = await self.data_fetcher.get_latest_data(symbol, timeframe)
            
            if data is None or len(data) < 50:
                logger.warning(f"❌ {symbol}_{timeframe} 數據不足")
                return None
            
            logger.info(f"📊 {symbol}_{timeframe} 獲取到 {len(data)} 條數據")
            
            # 計算信號
            signal_info = self.calculate_balanced_signals(data, strategy_config)
            
            if signal_info:
                logger.info(f"📊 {symbol}_{timeframe} 信號分析:")
                logger.info(f"   當前價格: {signal_info['current_price']:.6f}")
                logger.info(f"   RSI: {signal_info['current_rsi']:.2f}")
                logger.info(f"   TI: {signal_info['current_ti']:.4f}")
                logger.info(f"   BB上軌: {signal_info['bb_upper']:.6f}")
                logger.info(f"   BB中軌: {signal_info['bb_middle']:.6f}")
                logger.info(f"   BB下軌: {signal_info['bb_lower']:.6f}")
                logger.info(f"   價格動能: {signal_info['price_momentum']:.2f}%")
                
                logger.info(f"🔍 多頭條件分析:")
                for condition, result in signal_info['long_conditions'].items():
                    logger.info(f"   {condition}: {'✓' if result else '✗'}")
                logger.info(f"   多頭條件滿足: {signal_info['long_count']}/5")
                
                logger.info(f"🔍 空頭條件分析:")
                for condition, result in signal_info['short_conditions'].items():
                    logger.info(f"   {condition}: {'✓' if result else '✗'}")
                logger.info(f"   空頭條件滿足: {signal_info['short_count']}/5")
                
                if signal_info['signal']:
                    logger.info(f"🎯 信號: {signal_info['signal']} (強度: {signal_info['signal_strength']:.2f})")
                else:
                    logger.info(f"📊 無信號 (需要5個條件中至少4個)")
                
                return signal_info
            else:
                logger.warning(f"❌ {symbol}_{timeframe} 信號計算失敗")
                return None
                
        except Exception as e:
            logger.error(f"❌ {symbol}_{timeframe} 測試失敗: {e}")
            return None
    
    async def run_comprehensive_test(self):
        """運行全面測試"""
        logger.info("🚀 開始平衡版領先策略全面測試")
        logger.info("=" * 80)
        logger.info("📋 核心改進:")
        logger.info("   1. BB中軌作為領先指標，提前捕捉趨勢")
        logger.info("   2. TI動能確認，避免假突破")
        logger.info("   3. RSI過濾，避免極端區域開倉")
        logger.info("   4. 價格動能確認，確保趨勢強度")
        logger.info("=" * 80)
        
        all_results = []
        signal_count = 0
        
        for strategy_key, strategy_config in self.strategies.items():
            symbol = strategy_config['symbol']
            timeframe = strategy_config['timeframe']
            
            result = await self.test_single_strategy(symbol, timeframe, strategy_config)
            if result:
                all_results.append({
                    'strategy': strategy_key,
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'signal': result['signal'],
                    'signal_strength': result['signal_strength'],
                    'long_count': result['long_count'],
                    'short_count': result['short_count'],
                    'current_price': result['current_price'],
                    'rsi': result['current_rsi'],
                    'ti': result['current_ti'],
                    'price_momentum': result['price_momentum']
                })
                
                if result['signal']:
                    signal_count += 1
        
        # 生成測試報告
        self.generate_test_report(all_results, signal_count)
        
        return all_results
    
    def generate_test_report(self, results, signal_count):
        """生成測試報告"""
        if not results:
            logger.error("❌ 無測試結果")
            return
        
        print("\n" + "=" * 100)
        print("📊 平衡版領先策略測試報告")
        print("=" * 100)
        
        print(f"\n🎯 信號生成統計:")
        print(f"   測試策略數: {len(results)}")
        print(f"   產生信號數: {signal_count}")
        print(f"   信號生成率: {signal_count/len(results)*100:.1f}%")
        
        if signal_count > 0:
            print(f"\n🚨 當前活躍信號:")
            print("-" * 80)
            
            signal_data = []
            for result in results:
                if result['signal']:
                    signal_data.append({
                        '策略': result['strategy'],
                        '信號': result['signal'],
                        '強度': f"{result['signal_strength']:.2f}",
                        '價格': f"{result['current_price']:.6f}",
                        'RSI': f"{result['rsi']:.1f}",
                        'TI': f"{result['ti']:.4f}",
                        '動能': f"{result['price_momentum']:.2f}%",
                        '多頭條件': f"{result['long_count']}/5",
                        '空頭條件': f"{result['short_count']}/5"
                    })
            
            if signal_data:
                df = pd.DataFrame(signal_data)
                print(df.to_string(index=False))
        
        print(f"\n📋 策略條件滿足統計:")
        print("-" * 80)
        
        stats_data = []
        for result in results:
            stats_data.append({
                '策略': result['strategy'],
                '多頭條件': f"{result['long_count']}/5",
                '空頭條件': f"{result['short_count']}/5",
                '最高滿足': max(result['long_count'], result['short_count']),
                'RSI': f"{result['rsi']:.1f}",
                'TI': f"{result['ti']:.4f}",
                '價格動能': f"{result['price_momentum']:.2f}%"
            })
        
        stats_df = pd.DataFrame(stats_data)
        stats_df = stats_df.sort_values('最高滿足', ascending=False)
        print(stats_df.to_string(index=False))
        
        print(f"\n💡 策略改進效果:")
        print(f"   ✅ 使用BB中軌作為領先指標，提前捕捉趨勢轉折")
        print(f"   ✅ TI動能確認機制，過濾假突破信號")
        print(f"   ✅ RSI區間過濾，避免在極端區域開倉")
        print(f"   ✅ 價格動能確認，確保趨勢有足夠強度")
        print(f"   ✅ 平衡領先性和準確性，減少滯後問題")

async def main():
    """主函數"""
    strategy = BalancedLeadingStrategy()
    results = await strategy.run_comprehensive_test()
    
    if results:
        print(f"\n✅ 平衡版領先策略測試完成")
        print(f"📊 共測試 {len(results)} 個策略")
        print(f"🎯 這個版本應該能更好地平衡領先性和準確性")
    else:
        print(f"\n❌ 測試失敗，請檢查配置和數據")

if __name__ == "__main__":
    asyncio.run(main())
