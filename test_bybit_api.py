#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試Bybit API連接和數據獲取
"""

import asyncio
import aiohttp
import pandas as pd
from datetime import datetime, timedelta
import json

async def test_bybit_api():
    """測試Bybit API"""
    
    async with aiohttp.ClientSession() as session:
        # 測試基本連接
        url = "https://api.bybit.com/v5/market/kline"
        params = {
            "category": "linear",
            "symbol": "BTCUSDT",
            "interval": "60",  # 1小時
            "limit": 10
        }
        
        print("🔍 測試Bybit API連接...")
        print(f"URL: {url}")
        print(f"參數: {params}")
        
        try:
            async with session.get(url, params=params, timeout=30) as response:
                print(f"HTTP狀態碼: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    print(f"API返回碼: {data.get('retCode')}")
                    print(f"API消息: {data.get('retMsg')}")
                    
                    if data.get("retCode") == 0:
                        kline_data = data.get("result", {}).get("list", [])
                        print(f"獲取到 {len(kline_data)} 條K線數據")
                        
                        if kline_data:
                            print("\n📊 前3條數據樣本:")
                            for i, kline in enumerate(kline_data[:3]):
                                timestamp = datetime.fromtimestamp(int(kline[0]) / 1000)
                                print(f"  {i+1}. 時間: {timestamp}, 開盤: {kline[1]}, 最高: {kline[2]}, 最低: {kline[3]}, 收盤: {kline[4]}, 成交量: {kline[5]}")
                            
                            # 轉換為DataFrame測試
                            df = pd.DataFrame(kline_data)
                            df.columns = ["timestamp", "open", "high", "low", "close", "volume", "turnover"]
                            
                            # 數據類型轉換
                            for col in ["open", "high", "low", "close", "volume"]:
                                df[col] = pd.to_numeric(df[col])
                            
                            df["timestamp"] = pd.to_datetime(df["timestamp"].astype(float), unit="ms")
                            df = df.sort_values("timestamp").reset_index(drop=True)
                            df.set_index("timestamp", inplace=True)
                            
                            print(f"\n✅ DataFrame轉換成功，形狀: {df.shape}")
                            print("📈 DataFrame前5行:")
                            print(df.head())
                            
                            return True
                        else:
                            print("❌ 沒有獲取到K線數據")
                            return False
                    else:
                        print(f"❌ API錯誤: {data}")
                        return False
                else:
                    print(f"❌ HTTP錯誤: {response.status}")
                    text = await response.text()
                    print(f"響應內容: {text[:500]}")
                    return False
                    
        except Exception as e:
            print(f"❌ 請求異常: {e}")
            return False

async def test_multiple_symbols():
    """測試多個幣種"""
    symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', '1000PEPEUSDT']
    
    async with aiohttp.ClientSession() as session:
        for symbol in symbols:
            print(f"\n🧪 測試 {symbol}...")
            
            url = "https://api.bybit.com/v5/market/kline"
            params = {
                "category": "linear",
                "symbol": symbol,
                "interval": "60",
                "limit": 5
            }
            
            try:
                async with session.get(url, params=params, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("retCode") == 0:
                            kline_data = data.get("result", {}).get("list", [])
                            if kline_data:
                                latest_price = float(kline_data[0][4])  # 最新收盤價
                                print(f"  ✅ {symbol}: 最新價格 ${latest_price:,.2f}, 數據條數: {len(kline_data)}")
                            else:
                                print(f"  ❌ {symbol}: 無數據")
                        else:
                            print(f"  ❌ {symbol}: API錯誤 {data.get('retMsg')}")
                    else:
                        print(f"  ❌ {symbol}: HTTP錯誤 {response.status}")
                        
            except Exception as e:
                print(f"  ❌ {symbol}: 異常 {e}")
            
            await asyncio.sleep(0.2)  # 避免請求過快

async def test_historical_data():
    """測試歷史數據獲取"""
    print("\n🕐 測試歷史數據獲取...")
    
    # 計算時間範圍
    end_time = int(datetime.now().timestamp() * 1000)
    start_time = int((datetime.now() - timedelta(days=7)).timestamp() * 1000)
    
    print(f"時間範圍: {datetime.fromtimestamp(start_time/1000)} 到 {datetime.fromtimestamp(end_time/1000)}")
    
    async with aiohttp.ClientSession() as session:
        url = "https://api.bybit.com/v5/market/kline"
        params = {
            "category": "linear",
            "symbol": "BTCUSDT",
            "interval": "60",
            "limit": 200,
            "end": end_time
        }
        
        try:
            async with session.get(url, params=params, timeout=30) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("retCode") == 0:
                        kline_data = data.get("result", {}).get("list", [])
                        if kline_data:
                            print(f"✅ 獲取到 {len(kline_data)} 條歷史數據")
                            
                            # 檢查時間範圍
                            first_time = datetime.fromtimestamp(int(kline_data[-1][0]) / 1000)
                            last_time = datetime.fromtimestamp(int(kline_data[0][0]) / 1000)
                            print(f"數據時間範圍: {first_time} 到 {last_time}")
                            
                            # 檢查數據完整性
                            timestamps = [int(k[0]) for k in kline_data]
                            timestamps.sort()
                            
                            gaps = []
                            for i in range(1, len(timestamps)):
                                diff = timestamps[i] - timestamps[i-1]
                                expected_diff = 60 * 60 * 1000  # 1小時的毫秒數
                                if diff != expected_diff:
                                    gaps.append((timestamps[i-1], timestamps[i], diff))
                            
                            if gaps:
                                print(f"⚠️ 發現 {len(gaps)} 個數據間隙")
                            else:
                                print("✅ 數據連續性良好")
                            
                            return len(kline_data)
                        else:
                            print("❌ 沒有歷史數據")
                            return 0
                    else:
                        print(f"❌ API錯誤: {data}")
                        return 0
                else:
                    print(f"❌ HTTP錯誤: {response.status}")
                    return 0
                    
        except Exception as e:
            print(f"❌ 請求異常: {e}")
            return 0

async def main():
    """主函數"""
    print("🔧 Bybit API連接測試")
    print("=" * 50)
    
    # 基本連接測試
    success = await test_bybit_api()
    
    if success:
        # 多幣種測試
        await test_multiple_symbols()
        
        # 歷史數據測試
        data_count = await test_historical_data()
        
        print(f"\n📊 測試總結:")
        print(f"   基本連接: {'✅ 成功' if success else '❌ 失敗'}")
        print(f"   歷史數據: {data_count} 條記錄")
        
        if success and data_count > 100:
            print("\n🎉 API測試通過，可以進行回測！")
        else:
            print("\n⚠️ API測試有問題，需要檢查網絡或API配置")
    else:
        print("\n❌ 基本連接失敗，請檢查網絡連接")

if __name__ == "__main__":
    asyncio.run(main())
