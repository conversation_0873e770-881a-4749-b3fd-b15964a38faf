#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改良版策略回測系統
基於原始回測發現的問題，開發三個改良版策略進行測試
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os
import sys
import logging
import traceback
from typing import Dict, List, Optional, Tuple

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.data_fetcher import DataFetcher
from src.config_manager import ConfigManager

# 設置日誌
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('improved_strategies_backtest.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TechnicalIndicators:
    """技術指標計算類"""
    
    def bollinger_bands(self, prices, window=20, std_dev=2):
        """計算布林帶"""
        sma = prices.rolling(window=window).mean()
        std = prices.rolling(window=window).std()
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        return upper, sma, lower
    
    def rsi(self, prices, period=14):
        """計算RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def atr(self, high, low, close, period=14):
        """計算ATR"""
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(window=period).mean()
        return atr
    
    def obv(self, close, volume):
        """計算OBV (On-Balance Volume)"""
        obv = np.where(close > close.shift(1), volume, 
                      np.where(close < close.shift(1), -volume, 0))
        return pd.Series(obv, index=close.index).cumsum()
    
    def calculate_ti_confidence_interval(self, ti_values, lookback=24, confidence=0.7):
        """計算TI信賴區間"""
        rolling_ti = ti_values.rolling(window=lookback)
        upper_percentile = (1 + confidence) / 2
        lower_percentile = (1 - confidence) / 2
        
        upper_limit = rolling_ti.quantile(upper_percentile)
        lower_limit = rolling_ti.quantile(lower_percentile)
        
        return upper_limit, lower_limit

class HistoricalDataManager:
    """歷史數據管理器"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.data_fetcher = DataFetcher(config_manager)
        
    async def get_historical_data_range(self, symbol: str, timeframe: str, 
                                      start_date: datetime, end_date: datetime) -> Optional[pd.DataFrame]:
        """獲取指定時間範圍的歷史數據"""
        try:
            logger.info(f"獲取 {symbol} {timeframe} 歷史數據: {start_date.date()} 到 {end_date.date()}")
            
            # 計算需要的數據點數量
            if timeframe == '1h':
                total_hours = int((end_date - start_date).total_seconds() / 3600)
                required_points = min(total_hours, 2000)
            elif timeframe == '4h':
                total_4h_periods = int((end_date - start_date).total_seconds() / (4 * 3600))
                required_points = min(total_4h_periods, 1500)
            else:
                required_points = 730
            
            # 分批獲取數據
            all_data = []
            current_end = end_date
            batch_size = 200
            
            while len(all_data) < required_points and current_end > start_date:
                if timeframe == '1h':
                    batch_start = current_end - timedelta(hours=batch_size)
                elif timeframe == '4h':
                    batch_start = current_end - timedelta(hours=batch_size * 4)
                else:
                    batch_start = current_end - timedelta(days=batch_size)
                
                batch_start = max(batch_start, start_date)
                
                batch_data = await self.data_fetcher.get_latest_data(symbol, timeframe)
                
                if batch_data is not None and len(batch_data) > 0:
                    batch_data = batch_data[
                        (batch_data.index >= batch_start) & 
                        (batch_data.index <= current_end)
                    ]
                    
                    if len(batch_data) > 0:
                        all_data.append(batch_data)
                        logger.info(f"   獲取批次數據: {len(batch_data)} 條")
                
                current_end = batch_start - timedelta(minutes=1)
                await asyncio.sleep(0.1)
            
            if not all_data:
                logger.warning(f"❌ {symbol} {timeframe} 無法獲取歷史數據")
                return None
            
            combined_data = pd.concat(all_data, axis=0)
            combined_data = combined_data.sort_index().drop_duplicates()
            combined_data = combined_data[
                (combined_data.index >= start_date) & 
                (combined_data.index <= end_date)
            ]
            
            logger.info(f"✅ {symbol} {timeframe} 歷史數據獲取完成: {len(combined_data)} 條記錄")
            return combined_data
            
        except Exception as e:
            logger.error(f"❌ {symbol} {timeframe} 歷史數據獲取失敗: {e}")
            return None

class ImprovedStrategiesBacktester:
    """改良版策略回測器"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.data_manager = HistoricalDataManager(self.config_manager)
        self.indicators = TechnicalIndicators()
        
        # 加載策略配置
        self.strategies = self.load_strategies()
        
        # 回測結果存儲
        self.all_results = {}
        self.all_trade_records = {}
        
    def load_strategies(self) -> Dict:
        """加載所有策略配置"""
        try:
            with open('rsi_signal_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('active_strategies', {})
        except Exception as e:
            logger.error(f"加載策略配置失敗: {e}")
            return {}
    
    def calculate_supertrend_stop_loss(self, data: pd.DataFrame, direction: str, 
                                     entry_price: float, period: int = 10, 
                                     multiplier: float = 3.0) -> Optional[Dict]:
        """計算SUPERTREND止盈止損"""
        try:
            if len(data) < period:
                return None
                
            hl2 = (data['High'] + data['Low']) / 2
            atr = self.indicators.atr(data['High'], data['Low'], data['Close'], period=period)
            
            upper_band = hl2 + (multiplier * atr)
            lower_band = hl2 - (multiplier * atr)
            
            supertrend = np.where(data['Close'] <= lower_band.shift(1), lower_band, 
                                 np.where(data['Close'] >= upper_band.shift(1), upper_band, np.nan))
            
            supertrend = pd.Series(supertrend, index=data.index).fillna(method='ffill')
            
            if len(supertrend) == 0 or pd.isna(supertrend.iloc[-1]):
                return None
                
            current_supertrend = supertrend.iloc[-1]
            
            if direction == 'LONG':
                stop_loss = current_supertrend
                take_profit = entry_price + (abs(entry_price - current_supertrend) * 2.5)
            else:
                stop_loss = current_supertrend
                take_profit = entry_price - (abs(entry_price - current_supertrend) * 2.5)
            
            return {
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'risk_reward_ratio': 2.5,
                'method': 'SUPERTREND'
            }
            
        except Exception as e:
            logger.error(f"SUPERTREND計算失敗: {e}")
            return None

    def strategy1_simplified_ti_bb(self, data: pd.DataFrame, strategy_config: Dict) -> Optional[Dict]:
        """
        策略1：簡化版TI+BB中軌策略
        核心邏輯：主要依賴Taker Intensity + 布林帶中軌突破
        信號條件：4選3邏輯，減少條件嚴格度
        """
        try:
            if len(data) < 50:
                return None

            # 獲取策略參數
            bb_window = strategy_config.get('bb_window', 20)
            bb_std = strategy_config.get('bb_std', 2.0)
            ti_lookback = strategy_config.get('ti_lookback', 24)
            ti_confidence = strategy_config.get('ti_confidence', 0.7)

            # 計算技術指標
            bb_upper, bb_middle, bb_lower = self.indicators.bollinger_bands(
                data['Close'], window=bb_window, std_dev=bb_std
            )

            # 計算TI信賴區間
            ti_data = data['long_taker_intensity'] - data['short_taker_intensity']
            ti_upper_limit, ti_lower_limit = self.indicators.calculate_ti_confidence_interval(
                ti_data, lookback=ti_lookback, confidence=ti_confidence
            )

            # 獲取最新值
            current_price = data['Close'].iloc[-1]
            prev_price = data['Close'].iloc[-2] if len(data) > 1 else current_price
            current_ti = ti_data.iloc[-1]
            prev_ti = ti_data.iloc[-2] if len(ti_data) > 1 else current_ti
            current_bb_middle = bb_middle.iloc[-1]
            prev_bb_middle = bb_middle.iloc[-2] if len(bb_middle) > 1 else current_bb_middle

            # 簡化版多頭信號條件 (4選3)
            long_conditions = {
                'bb_middle_breakout': current_price > current_bb_middle and prev_price <= prev_bb_middle,
                'ti_positive': current_ti > 0,
                'ti_momentum_up': current_ti > prev_ti,
                'ti_strong': current_ti > ti_upper_limit.iloc[-1] if not pd.isna(ti_upper_limit.iloc[-1]) else False
            }

            # 簡化版空頭信號條件 (4選3)
            short_conditions = {
                'bb_middle_breakdown': current_price < current_bb_middle and prev_price >= prev_bb_middle,
                'ti_negative': current_ti < 0,
                'ti_momentum_down': current_ti < prev_ti,
                'ti_weak': current_ti < ti_lower_limit.iloc[-1] if not pd.isna(ti_lower_limit.iloc[-1]) else False
            }

            # 計算滿足條件數量
            long_count = sum(long_conditions.values())
            short_count = sum(short_conditions.values())

            # 生成信號 (需要4個條件中滿足3個)
            signal = None
            signal_strength = 0

            if long_count >= 3:
                signal = 'LONG'
                signal_strength = long_count / 4
            elif short_count >= 3:
                signal = 'SHORT'
                signal_strength = short_count / 4

            return {
                'signal': signal,
                'signal_strength': signal_strength,
                'current_price': current_price,
                'current_ti': current_ti,
                'bb_middle': current_bb_middle,
                'long_conditions': long_conditions,
                'short_conditions': short_conditions,
                'long_count': long_count,
                'short_count': short_count,
                'strategy_name': 'Simplified_TI_BB'
            }

        except Exception as e:
            logger.error(f"策略1計算失敗: {e}")
            return None

    def strategy2_contrarian_rsi_bb(self, data: pd.DataFrame, strategy_config: Dict) -> Optional[Dict]:
        """
        策略2：逆勢RSI+BB中軌策略
        修改RSI邏輯為逆勢操作：RSI<30時做多、RSI>70時做空
        """
        try:
            if len(data) < 50:
                return None

            # 獲取策略參數
            bb_window = strategy_config.get('bb_window', 20)
            bb_std = strategy_config.get('bb_std', 2.0)
            rsi_period = strategy_config.get('rsi_period', 14)
            ti_lookback = strategy_config.get('ti_lookback', 24)

            # 計算技術指標
            bb_upper, bb_middle, bb_lower = self.indicators.bollinger_bands(
                data['Close'], window=bb_window, std_dev=bb_std
            )
            rsi = self.indicators.rsi(data['Close'], period=rsi_period)

            # 計算TI數據
            ti_data = data['long_taker_intensity'] - data['short_taker_intensity']

            # 獲取最新值
            current_price = data['Close'].iloc[-1]
            prev_price = data['Close'].iloc[-2] if len(data) > 1 else current_price
            current_rsi = rsi.iloc[-1]
            current_ti = ti_data.iloc[-1]
            prev_ti = ti_data.iloc[-2] if len(ti_data) > 1 else current_ti
            current_bb_middle = bb_middle.iloc[-1]
            prev_bb_middle = bb_middle.iloc[-2] if len(bb_middle) > 1 else current_bb_middle

            # 逆勢多頭信號條件 (4選3)
            long_conditions = {
                'rsi_oversold': current_rsi < 30,  # 逆勢：RSI超賣時做多
                'bb_middle_support': current_price > current_bb_middle,
                'ti_positive_momentum': current_ti > 0 and current_ti > prev_ti,
                'price_near_bb_middle': abs(current_price - current_bb_middle) / current_bb_middle < 0.02
            }

            # 逆勢空頭信號條件 (4選3)
            short_conditions = {
                'rsi_overbought': current_rsi > 70,  # 逆勢：RSI超買時做空
                'bb_middle_resistance': current_price < current_bb_middle,
                'ti_negative_momentum': current_ti < 0 and current_ti < prev_ti,
                'price_near_bb_middle': abs(current_price - current_bb_middle) / current_bb_middle < 0.02
            }

            # 計算滿足條件數量
            long_count = sum(long_conditions.values())
            short_count = sum(short_conditions.values())

            # 生成信號 (需要4個條件中滿足3個)
            signal = None
            signal_strength = 0

            if long_count >= 3:
                signal = 'LONG'
                signal_strength = long_count / 4
            elif short_count >= 3:
                signal = 'SHORT'
                signal_strength = short_count / 4

            return {
                'signal': signal,
                'signal_strength': signal_strength,
                'current_price': current_price,
                'current_rsi': current_rsi,
                'current_ti': current_ti,
                'bb_middle': current_bb_middle,
                'long_conditions': long_conditions,
                'short_conditions': short_conditions,
                'long_count': long_count,
                'short_count': short_count,
                'strategy_name': 'Contrarian_RSI_BB'
            }

        except Exception as e:
            logger.error(f"策略2計算失敗: {e}")
            return None

    def strategy3_obv_ti_bb(self, data: pd.DataFrame, strategy_config: Dict) -> Optional[Dict]:
        """
        策略3：OBV成交量+TI+BB策略
        完全移除RSI指標，新增OBV成交量指標
        """
        try:
            if len(data) < 50:
                return None

            # 獲取策略參數
            bb_window = strategy_config.get('bb_window', 20)
            bb_std = strategy_config.get('bb_std', 2.0)
            ti_lookback = strategy_config.get('ti_lookback', 24)
            obv_period = strategy_config.get('obv_period', 10)

            # 計算技術指標
            bb_upper, bb_middle, bb_lower = self.indicators.bollinger_bands(
                data['Close'], window=bb_window, std_dev=bb_std
            )

            # 計算OBV和其移動平均
            obv = self.indicators.obv(data['Close'], data['Volume'])
            obv_ma = obv.rolling(window=obv_period).mean()

            # 計算TI數據
            ti_data = data['long_taker_intensity'] - data['short_taker_intensity']
            ti_upper_limit, ti_lower_limit = self.indicators.calculate_ti_confidence_interval(
                ti_data, lookback=ti_lookback, confidence=0.7
            )

            # 獲取最新值
            current_price = data['Close'].iloc[-1]
            prev_price = data['Close'].iloc[-2] if len(data) > 1 else current_price
            current_ti = ti_data.iloc[-1]
            prev_ti = ti_data.iloc[-2] if len(ti_data) > 1 else current_ti
            current_bb_middle = bb_middle.iloc[-1]
            prev_bb_middle = bb_middle.iloc[-2] if len(bb_middle) > 1 else current_bb_middle
            current_obv = obv.iloc[-1]
            current_obv_ma = obv_ma.iloc[-1]
            prev_obv_ma = obv_ma.iloc[-2] if len(obv_ma) > 1 else current_obv_ma

            # OBV多頭信號條件 (4選3)
            long_conditions = {
                'bb_middle_breakout': current_price > current_bb_middle and prev_price <= prev_bb_middle,
                'ti_positive_strong': current_ti > 0 and current_ti > ti_upper_limit.iloc[-1] if not pd.isna(ti_upper_limit.iloc[-1]) else current_ti > 0,
                'obv_rising': current_obv > current_obv_ma and current_obv_ma > prev_obv_ma,
                'volume_confirmation': data['Volume'].iloc[-1] > data['Volume'].rolling(10).mean().iloc[-1]
            }

            # OBV空頭信號條件 (4選3)
            short_conditions = {
                'bb_middle_breakdown': current_price < current_bb_middle and prev_price >= prev_bb_middle,
                'ti_negative_strong': current_ti < 0 and current_ti < ti_lower_limit.iloc[-1] if not pd.isna(ti_lower_limit.iloc[-1]) else current_ti < 0,
                'obv_falling': current_obv < current_obv_ma and current_obv_ma < prev_obv_ma,
                'volume_confirmation': data['Volume'].iloc[-1] > data['Volume'].rolling(10).mean().iloc[-1]
            }

            # 計算滿足條件數量
            long_count = sum(long_conditions.values())
            short_count = sum(short_conditions.values())

            # 生成信號 (需要4個條件中滿足3個)
            signal = None
            signal_strength = 0

            if long_count >= 3:
                signal = 'LONG'
                signal_strength = long_count / 4
            elif short_count >= 3:
                signal = 'SHORT'
                signal_strength = short_count / 4

            return {
                'signal': signal,
                'signal_strength': signal_strength,
                'current_price': current_price,
                'current_ti': current_ti,
                'current_obv': current_obv,
                'bb_middle': current_bb_middle,
                'long_conditions': long_conditions,
                'short_conditions': short_conditions,
                'long_count': long_count,
                'short_count': short_count,
                'strategy_name': 'OBV_TI_BB'
            }

        except Exception as e:
            logger.error(f"策略3計算失敗: {e}")
            return None

    def simulate_trade_exit(self, data: pd.DataFrame, entry_index: int, trade: Dict) -> Optional[Dict]:
        """模擬交易平倉"""
        try:
            entry_price = trade['entry_price']
            stop_loss = trade['stop_loss']
            take_profit = trade['take_profit']
            direction = trade['direction']

            for i in range(entry_index + 1, len(data)):
                current_high = data['High'].iloc[i]
                current_low = data['Low'].iloc[i]
                current_close = data['Close'].iloc[i]

                if direction == 'LONG':
                    if current_low <= stop_loss:
                        pnl_pct = (stop_loss - entry_price) / entry_price * 100
                        return {
                            'exit_price': stop_loss,
                            'exit_time': data.index[i],
                            'exit_type': 'STOP_LOSS',
                            'pnl_pct': pnl_pct,
                            'duration_hours': (data.index[i] - trade['timestamp']).total_seconds() / 3600
                        }
                    elif current_high >= take_profit:
                        pnl_pct = (take_profit - entry_price) / entry_price * 100
                        return {
                            'exit_price': take_profit,
                            'exit_time': data.index[i],
                            'exit_type': 'TAKE_PROFIT',
                            'pnl_pct': pnl_pct,
                            'duration_hours': (data.index[i] - trade['timestamp']).total_seconds() / 3600
                        }
                else:  # SHORT
                    if current_high >= stop_loss:
                        pnl_pct = (entry_price - stop_loss) / entry_price * 100
                        return {
                            'exit_price': stop_loss,
                            'exit_time': data.index[i],
                            'exit_type': 'STOP_LOSS',
                            'pnl_pct': pnl_pct,
                            'duration_hours': (data.index[i] - trade['timestamp']).total_seconds() / 3600
                        }
                    elif current_low <= take_profit:
                        pnl_pct = (entry_price - take_profit) / entry_price * 100
                        return {
                            'exit_price': take_profit,
                            'exit_time': data.index[i],
                            'exit_type': 'TAKE_PROFIT',
                            'pnl_pct': pnl_pct,
                            'duration_hours': (data.index[i] - trade['timestamp']).total_seconds() / 3600
                        }

            # 如果沒有觸發止盈止損，用最後價格平倉
            final_price = data['Close'].iloc[-1]
            if direction == 'LONG':
                pnl_pct = (final_price - entry_price) / entry_price * 100
            else:
                pnl_pct = (entry_price - final_price) / entry_price * 100

            return {
                'exit_price': final_price,
                'exit_time': data.index[-1],
                'exit_type': 'MARKET_CLOSE',
                'pnl_pct': pnl_pct,
                'duration_hours': (data.index[-1] - trade['timestamp']).total_seconds() / 3600
            }

        except Exception as e:
            logger.error(f"模擬交易平倉失敗: {e}")
            return None

    async def backtest_single_strategy_with_method(self, strategy_key: str, strategy_config: Dict,
                                                  strategy_method: str) -> Optional[Dict]:
        """使用指定方法回測單個策略"""
        symbol = strategy_config['symbol']
        timeframe = strategy_config['timeframe']

        logger.info(f"🔍 開始回測策略: {strategy_key} ({symbol} {timeframe}) - 方法: {strategy_method}")

        try:
            # 設置回測時間範圍
            end_date = datetime.now()
            start_date = end_date - timedelta(days=365)  # 1年數據

            # 獲取歷史數據
            data = await self.data_manager.get_historical_data_range(
                symbol, timeframe, start_date, end_date
            )

            if data is None or len(data) < 200:
                logger.warning(f"❌ {strategy_key} 數據不足，跳過回測")
                return None

            logger.info(f"📊 {strategy_key} 開始回測，數據量: {len(data)} 條")

            # 選擇策略方法
            if strategy_method == 'strategy1':
                strategy_func = self.strategy1_simplified_ti_bb
            elif strategy_method == 'strategy2':
                strategy_func = self.strategy2_contrarian_rsi_bb
            elif strategy_method == 'strategy3':
                strategy_func = self.strategy3_obv_ti_bb
            else:
                logger.error(f"未知策略方法: {strategy_method}")
                return None

            # 執行回測
            trades = []

            # 從第50個數據點開始回測，確保指標計算穩定
            for i in range(50, len(data)):
                current_data = data.iloc[:i+1]

                # 計算信號
                signal_info = strategy_func(current_data, strategy_config)

                if signal_info and signal_info['signal']:
                    # 計算止盈止損
                    stop_info = self.calculate_supertrend_stop_loss(
                        current_data, signal_info['signal'], signal_info['current_price']
                    )

                    if stop_info:
                        trade = {
                            'strategy': strategy_key,
                            'strategy_method': strategy_method,
                            'timestamp': current_data.index[-1],
                            'symbol': symbol,
                            'timeframe': timeframe,
                            'direction': signal_info['signal'],
                            'entry_price': signal_info['current_price'],
                            'stop_loss': stop_info['stop_loss'],
                            'take_profit': stop_info['take_profit'],
                            'signal_strength': signal_info['signal_strength'],
                            'strategy_name': signal_info['strategy_name']
                        }

                        # 添加策略特定信息
                        if 'current_rsi' in signal_info:
                            trade['rsi'] = signal_info['current_rsi']
                        if 'current_ti' in signal_info:
                            trade['ti'] = signal_info['current_ti']
                        if 'current_obv' in signal_info:
                            trade['obv'] = signal_info['current_obv']

                        trade['long_count'] = signal_info['long_count']
                        trade['short_count'] = signal_info['short_count']

                        # 模擬交易結果
                        exit_result = self.simulate_trade_exit(data, i, trade)
                        if exit_result:
                            trade.update(exit_result)
                            trades.append(trade)

            # 計算策略績效
            if trades:
                performance = self.calculate_performance_metrics(trades, strategy_key, strategy_method)
                logger.info(f"✅ {strategy_key} ({strategy_method}) 回測完成: {len(trades)}筆交易, 總回報: {performance['total_return']:.2f}%")
                return performance
            else:
                logger.warning(f"⚠️ {strategy_key} ({strategy_method}) 無交易信號")
                return {
                    'strategy': strategy_key,
                    'strategy_method': strategy_method,
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'total_trades': 0,
                    'total_return': 0,
                    'win_rate': 0,
                    'max_drawdown': 0,
                    'sharpe_ratio': 0,
                    'profit_factor': 0,
                    'signal_frequency': 0
                }

        except Exception as e:
            logger.error(f"❌ {strategy_key} ({strategy_method}) 回測失敗: {e}")
            logger.error(traceback.format_exc())
            return None

    def calculate_performance_metrics(self, trades: List[Dict], strategy_key: str, strategy_method: str) -> Dict:
        """計算策略績效指標"""
        if not trades:
            return {}

        df = pd.DataFrame(trades)

        # 基本統計
        total_trades = len(df)
        winning_trades = len(df[df['pnl_pct'] > 0])
        losing_trades = len(df[df['pnl_pct'] <= 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        # 盈虧統計
        total_pnl = df['pnl_pct'].sum()
        avg_win = df[df['pnl_pct'] > 0]['pnl_pct'].mean() if winning_trades > 0 else 0
        avg_loss = df[df['pnl_pct'] <= 0]['pnl_pct'].mean() if losing_trades > 0 else 0

        # 最大回撤
        cumulative_pnl = df['pnl_pct'].cumsum()
        running_max = cumulative_pnl.expanding().max()
        drawdown = cumulative_pnl - running_max
        max_drawdown = drawdown.min()

        # 盈利因子
        gross_profit = df[df['pnl_pct'] > 0]['pnl_pct'].sum()
        gross_loss = abs(df[df['pnl_pct'] <= 0]['pnl_pct'].sum())
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')

        # 夏普比率
        returns_std = df['pnl_pct'].std()
        sharpe_ratio = (df['pnl_pct'].mean() / returns_std) if returns_std > 0 else 0

        # 信號頻率
        if len(df) > 0:
            time_span = (df['timestamp'].max() - df['timestamp'].min()).days
            signal_frequency = total_trades / max(time_span, 1)
        else:
            signal_frequency = 0

        # 平均持倉時間
        avg_duration = df['duration_hours'].mean() if 'duration_hours' in df.columns else 0

        # 止盈止損統計
        tp_count = len(df[df['exit_type'] == 'TAKE_PROFIT'])
        sl_count = len(df[df['exit_type'] == 'STOP_LOSS'])
        tp_rate = tp_count / total_trades if total_trades > 0 else 0
        sl_rate = sl_count / total_trades if total_trades > 0 else 0

        return {
            'strategy': strategy_key,
            'strategy_method': strategy_method,
            'symbol': df['symbol'].iloc[0],
            'timeframe': df['timeframe'].iloc[0],
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'total_return': total_pnl,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'max_drawdown': max_drawdown,
            'profit_factor': profit_factor,
            'sharpe_ratio': sharpe_ratio,
            'signal_frequency': signal_frequency,
            'avg_duration_hours': avg_duration,
            'take_profit_rate': tp_rate,
            'stop_loss_rate': sl_rate,
            'first_trade': df['timestamp'].min(),
            'last_trade': df['timestamp'].max()
        }

    async def run_comprehensive_strategy_comparison(self) -> Dict:
        """運行三個策略的全面對比回測"""
        logger.info("🚀 開始改良版策略全面對比回測")
        logger.info("=" * 120)
        logger.info("📋 回測配置:")
        logger.info("   - 策略數量: 3個改良版策略")
        logger.info("   - 測試範圍: 19個活躍策略配置")
        logger.info("   - 回測期間: 1年歷史數據")
        logger.info("   - 策略1: 簡化版TI+BB中軌策略 (4選3)")
        logger.info("   - 策略2: 逆勢RSI+BB中軌策略 (4選3)")
        logger.info("   - 策略3: OBV成交量+TI+BB策略 (4選3)")
        logger.info("=" * 120)

        strategy_methods = ['strategy1', 'strategy2', 'strategy3']
        all_results = []
        all_trade_records = []

        for method in strategy_methods:
            logger.info(f"\n🔍 開始測試策略方法: {method}")
            method_results = []
            method_trades = []

            for strategy_key, strategy_config in self.strategies.items():
                try:
                    result = await self.backtest_single_strategy_with_method(
                        strategy_key, strategy_config, method
                    )

                    if result:
                        method_results.append(result)
                        all_results.append(result)

                        # 收集交易記錄
                        if result['total_trades'] > 0:
                            # 這裡需要從實際交易記錄中獲取
                            pass

                    # 避免過度請求API
                    await asyncio.sleep(0.5)

                except Exception as e:
                    logger.error(f"❌ {strategy_key} ({method}) 回測異常: {e}")
                    continue

            # 存儲方法結果
            self.all_results[method] = method_results
            logger.info(f"✅ {method} 完成: {len(method_results)} 個策略有結果")

        # 生成綜合報告
        if all_results:
            self.generate_strategy_comparison_report(all_results)
            self.save_results_to_files(all_results, all_trade_records)

            logger.info(f"✅ 改良版策略對比回測完成!")
            logger.info(f"📊 總結果數: {len(all_results)}")
        else:
            logger.error("❌ 所有策略回測失敗")

        return {
            'results': all_results,
            'trade_records': all_trade_records,
            'summary': {
                'total_strategies': len(self.strategies),
                'total_methods': len(strategy_methods),
                'successful_backtests': len(all_results)
            }
        }

    def generate_strategy_comparison_report(self, results: List[Dict]):
        """生成策略對比報告"""
        if not results:
            logger.error("❌ 無回測結果，無法生成報告")
            return

        print("\n" + "=" * 120)
        print("📊 改良版策略對比回測報告")
        print("=" * 120)

        # 按策略方法分組
        strategy_groups = {}
        for result in results:
            method = result['strategy_method']
            if method not in strategy_groups:
                strategy_groups[method] = []
            strategy_groups[method].append(result)

        # 策略方法名稱映射
        method_names = {
            'strategy1': '簡化版TI+BB中軌策略',
            'strategy2': '逆勢RSI+BB中軌策略',
            'strategy3': 'OBV成交量+TI+BB策略'
        }

        # 為每個策略方法生成報告
        for method, method_results in strategy_groups.items():
            print(f"\n🎯 {method_names.get(method, method)} 回測結果:")
            print("-" * 100)

            # 過濾有交易的結果
            trading_results = [r for r in method_results if r['total_trades'] > 0]

            if not trading_results:
                print(f"   ❌ 該策略無任何交易信號")
                continue

            # 計算總體統計
            total_trades = sum(r['total_trades'] for r in trading_results)
            avg_win_rate = np.mean([r['win_rate'] for r in trading_results])
            avg_return = np.mean([r['total_return'] for r in trading_results])
            avg_sharpe = np.mean([r['sharpe_ratio'] for r in trading_results if not np.isnan(r['sharpe_ratio'])])
            avg_signal_freq = np.mean([r['signal_frequency'] for r in trading_results])

            print(f"   📈 有效策略數: {len(trading_results)}/{len(method_results)}")
            print(f"   📊 總交易筆數: {total_trades}")
            print(f"   🎯 平均勝率: {avg_win_rate:.1%}")
            print(f"   💰 平均總回報: {avg_return:.2f}%")
            print(f"   📊 平均夏普比率: {avg_sharpe:.2f}")
            print(f"   ⚡ 平均信號頻率: {avg_signal_freq:.2f}/天")

            # 顯示前5名表現最好的策略
            top_strategies = sorted(trading_results, key=lambda x: x['total_return'], reverse=True)[:5]

            print(f"\n   🏆 前5名表現:")
            for i, strategy in enumerate(top_strategies, 1):
                print(f"   {i}. {strategy['strategy']} ({strategy['symbol']} {strategy['timeframe']}): "
                      f"回報 {strategy['total_return']:.2f}%, 勝率 {strategy['win_rate']:.1%}, "
                      f"交易數 {strategy['total_trades']}")

        # 策略方法對比
        print(f"\n📊 策略方法綜合對比:")
        print("-" * 100)

        comparison_data = []
        for method, method_results in strategy_groups.items():
            trading_results = [r for r in method_results if r['total_trades'] > 0]

            if trading_results:
                comparison_data.append({
                    '策略方法': method_names.get(method, method),
                    '有效策略數': len(trading_results),
                    '總交易數': sum(r['total_trades'] for r in trading_results),
                    '平均勝率': f"{np.mean([r['win_rate'] for r in trading_results]):.1%}",
                    '平均回報': f"{np.mean([r['total_return'] for r in trading_results]):.2f}%",
                    '平均夏普比率': f"{np.mean([r['sharpe_ratio'] for r in trading_results if not np.isnan(r['sharpe_ratio'])]):.2f}",
                    '平均信號頻率': f"{np.mean([r['signal_frequency'] for r in trading_results]):.2f}/天"
                })

        if comparison_data:
            df_comparison = pd.DataFrame(comparison_data)
            print(df_comparison.to_string(index=False))

        # 結論和建議
        print(f"\n💡 策略改進效果分析:")
        print("-" * 80)

        best_method = None
        best_signal_count = 0

        for method, method_results in strategy_groups.items():
            trading_results = [r for r in method_results if r['total_trades'] > 0]
            total_signals = sum(r['total_trades'] for r in trading_results)

            if total_signals > best_signal_count:
                best_signal_count = total_signals
                best_method = method

        if best_method:
            print(f"   🏆 最佳策略: {method_names.get(best_method, best_method)}")
            print(f"   📊 總信號數: {best_signal_count}")
            print(f"   ✅ 成功解決了原始策略無信號的問題")
        else:
            print(f"   ❌ 所有改良策略仍無法產生足夠信號")
            print(f"   💡 建議進一步降低信號條件或調整參數")

        print(f"\n⚠️ 注意事項:")
        print("-" * 80)
        print(f"   - 回測基於1年歷史數據，實際表現可能不同")
        print(f"   - 建議在實盤前進行紙上交易驗證")
        print(f"   - 定期監控策略表現並調整參數")
        print(f"   - 注意風險管理和資金配置")

    def save_results_to_files(self, results: List[Dict], trade_records: List[Dict]):
        """保存結果到文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存詳細結果到JSON
            results_file = f"improved_strategies_results_{timestamp}.json"
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)

            # 保存交易記錄到CSV
            if trade_records:
                trades_file = f"improved_strategies_trades_{timestamp}.csv"
                df_trades = pd.DataFrame(trade_records)
                df_trades.to_csv(trades_file, index=False, encoding='utf-8-sig')
                logger.info(f"💾 交易記錄已保存: {trades_file}")

            # 保存績效摘要到CSV
            summary_file = f"improved_strategies_summary_{timestamp}.csv"
            df_summary = pd.DataFrame(results)
            df_summary.to_csv(summary_file, index=False, encoding='utf-8-sig')

            # 按策略方法分別保存
            strategy_groups = {}
            for result in results:
                method = result['strategy_method']
                if method not in strategy_groups:
                    strategy_groups[method] = []
                strategy_groups[method].append(result)

            for method, method_results in strategy_groups.items():
                method_file = f"improved_strategy_{method}_results_{timestamp}.csv"
                df_method = pd.DataFrame(method_results)
                df_method.to_csv(method_file, index=False, encoding='utf-8-sig')
                logger.info(f"💾 {method} 結果已保存: {method_file}")

            logger.info(f"💾 回測結果已保存:")
            logger.info(f"   - 詳細結果: {results_file}")
            logger.info(f"   - 績效摘要: {summary_file}")

        except Exception as e:
            logger.error(f"❌ 保存結果失敗: {e}")

async def main():
    """主函數"""
    try:
        backtest_system = ImprovedStrategiesBacktester()

        logger.info("🚀 啟動改良版策略對比回測系統")
        logger.info(f"📊 將測試 3 個改良策略對 {len(backtest_system.strategies)} 個策略配置")

        # 執行全面回測
        results = await backtest_system.run_comprehensive_strategy_comparison()

        if results['results']:
            logger.info("✅ 改良版策略對比回測系統執行完成")
            logger.info(f"📈 成功回測結果: {len(results['results'])}")

            # 統計每個策略方法的結果
            method_stats = {}
            for result in results['results']:
                method = result['strategy_method']
                if method not in method_stats:
                    method_stats[method] = {'total': 0, 'with_trades': 0}
                method_stats[method]['total'] += 1
                if result['total_trades'] > 0:
                    method_stats[method]['with_trades'] += 1

            logger.info("📊 各策略方法統計:")
            for method, stats in method_stats.items():
                logger.info(f"   {method}: {stats['with_trades']}/{stats['total']} 個策略有交易信號")

        else:
            logger.error("❌ 回測系統執行失敗")

    except Exception as e:
        logger.error(f"❌ 系統執行異常: {e}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    asyncio.run(main())
