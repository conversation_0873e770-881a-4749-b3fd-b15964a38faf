# OBV+TI+BB策略實盤交易系統

## 📊 策略概述

**OBV成交量+TI+BB策略**是基於深度回測驗證的高勝率量化交易策略，專為加密貨幣市場設計。

### 🏆 核心優勢
- **78.7%勝率** - 經過19個策略配置的全面回測驗證
- **1.98夏普比率** - 優秀的風險調整後收益
- **13/19策略有效** - 高達68.4%的策略適用性
- **實時信號生成** - 每分鐘掃描，及時捕捉市場機會

## 🔧 策略邏輯

### 信號生成條件 (4選3邏輯)

#### 多頭信號條件：
1. **BB中軌突破**: 價格突破布林帶中軌向上
2. **TI強勢確認**: Taker Intensity為正且超過70%信賴區間上限
3. **OBV上升趨勢**: OBV高於其移動平均且呈上升趨勢
4. **成交量確認**: 當前成交量高於10期平均成交量

#### 空頭信號條件：
1. **BB中軌跌破**: 價格跌破布林帶中軌向下
2. **TI弱勢確認**: Taker Intensity為負且低於70%信賴區間下限
3. **OBV下降趨勢**: OBV低於其移動平均且呈下降趨勢
4. **成交量確認**: 當前成交量高於10期平均成交量

### 止盈止損機制
- **方法**: SUPERTREND動態計算
- **風險收益比**: 2.5:1
- **ATR週期**: 10
- **ATR倍數**: 3.0

## 🚀 快速開始

### 1. 環境準備
```bash
# 確保Python 3.8+環境
python --version

# 安裝依賴包
pip install pandas numpy aiohttp requests asyncio
```

### 2. 配置設置
1. 確保`src/`目錄包含必要模組：
   - `config_manager.py`
   - `data_fetcher.py`

2. 配置Telegram通知（可選）：
   - 在`src/config_manager.py`中設置bot_token和chat_id

3. 檢查策略配置文件：
   - `rsi_signal_config.json` - 主策略配置
   - `obv_ti_bb_config.json` - OBV策略專用配置

### 3. 啟動系統
```bash
# 使用啟動腳本
python start_obv_ti_bb_system.py

# 或直接運行主系統
python obv_ti_bb_production_system.py
```

## 📋 系統功能

### 核心功能
- ✅ **實時信號掃描** - 每分鐘掃描所有配置的策略
- ✅ **自動交易管理** - 自動記錄開倉和平倉
- ✅ **Telegram通知** - 實時信號和平倉通知
- ✅ **風險控制** - 每個幣種最多1個活躍交易
- ✅ **數據持久化** - 交易記錄和信號記錄自動保存

### 監控策略
系統默認監控以下高表現策略：
- **SEIUSDT_1H** - 522.40%回報，100%勝率
- **BTCUSDT_4H** - 327.48%回報，97.7%勝率
- **SANDUSDT_1H** - 216.83%回報，91.4%勝率
- **JUPUSDT_4H** - 198.05%回報，76.7%勝率
- **XRPUSDT_4H** - 134.99%回報，72.4%勝率
- 以及其他8個驗證有效的策略配置

## 📊 文件結構

```
QUANT/
├── obv_ti_bb_production_system.py    # 主交易系統
├── start_obv_ti_bb_system.py         # 啟動腳本
├── obv_ti_bb_config.json             # 策略配置
├── rsi_signal_config.json            # 主配置文件
├── src/
│   ├── config_manager.py              # 配置管理
│   ├── data_fetcher.py                # 數據獲取
│   └── ...
├── logs/                              # 日誌目錄
├── obv_ti_bb_trades.json             # 交易記錄
├── obv_ti_bb_signals.csv             # 信號記錄
└── OBV_TI_BB_README.md               # 本文件
```

## 🔍 系統監控

### 日誌監控
```bash
# 實時查看日誌
tail -f logs/obv_ti_bb_$(date +%Y%m%d).log

# 查看交易記錄
cat obv_ti_bb_trades.json | jq .
```

### Telegram通知
系統會自動發送以下通知：
- 🚀 **交易信號** - 包含入場價、止盈、止損
- 🎯 **平倉通知** - 包含盈虧結果和平倉原因
- 📊 **狀態報告** - 每小時系統狀態摘要

## ⚠️ 風險提示

1. **歷史績效不代表未來表現** - 市場環境變化可能影響策略效果
2. **實盤交易成本** - 需考慮滑點、手續費等實際交易成本
3. **資金管理** - 建議合理分配資金，控制單筆交易風險
4. **系統監控** - 定期檢查系統運行狀態和策略表現
5. **參數調整** - 根據市場變化適時調整策略參數

## 🛠️ 故障排除

### 常見問題
1. **無法獲取數據** - 檢查網絡連接和API配置
2. **無交易信號** - 確認策略配置和市場條件
3. **Telegram通知失敗** - 檢查bot_token和chat_id配置
4. **系統崩潰** - 查看日誌文件定位問題

### 技術支持
- 查看日誌文件獲取詳細錯誤信息
- 檢查配置文件格式和參數設置
- 確保所有依賴模組正確安裝

## 📈 性能優化建議

1. **參數調優** - 根據不同市場環境調整BB、TI、OBV參數
2. **策略組合** - 可與其他策略組合使用，分散風險
3. **時間框架** - 根據交易偏好選擇1H或4H時間框架
4. **幣種選擇** - 專注於歷史表現優異的幣種

---

**免責聲明**: 本系統僅供學習和研究使用，不構成投資建議。使用者需自行承擔交易風險。
