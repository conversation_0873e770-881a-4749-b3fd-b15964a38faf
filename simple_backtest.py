#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化版真實數據回測
專注於測試止盈止損方法的效果
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os
from typing import Dict, List, Optional, Tuple

from stop_loss_take_profit_research import StopLossTakeProfitCalculator, StopLossResult, StopLossType

class SimpleBacktester:
    """簡化版回測器"""
    
    def __init__(self):
        self.calculator = StopLossTakeProfitCalculator()
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_data(self, symbol: str, timeframe: str = "1h", limit: int = 500) -> Optional[pd.DataFrame]:
        """獲取數據"""
        try:
            interval_map = {'1h': '60', '4h': '240'}
            interval = interval_map.get(timeframe, '60')
            
            url = "https://api.bybit.com/v5/market/kline"
            params = {
                "category": "linear",
                "symbol": symbol,
                "interval": interval,
                "limit": limit
            }
            
            async with self.session.get(url, params=params, timeout=30) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("retCode") == 0:
                        kline_data = data.get("result", {}).get("list", [])
                        
                        if kline_data and len(kline_data) >= 100:
                            df = pd.DataFrame(kline_data)
                            df.columns = ["timestamp", "open", "high", "low", "close", "volume", "turnover"]
                            
                            for col in ["open", "high", "low", "close", "volume"]:
                                df[col] = pd.to_numeric(df[col])
                            
                            df["timestamp"] = pd.to_datetime(df["timestamp"].astype(float), unit="ms")
                            df = df.sort_values("timestamp").reset_index(drop=True)
                            df.set_index("timestamp", inplace=True)
                            
                            print(f"✅ {symbol} {timeframe}: 獲取 {len(df)} 條數據")
                            return df
                        else:
                            print(f"❌ {symbol} {timeframe}: 數據不足 ({len(kline_data) if kline_data else 0} 條)")
                            return None
                    else:
                        print(f"❌ {symbol} API錯誤: {data}")
                        return None
                else:
                    print(f"❌ {symbol} HTTP錯誤: {response.status}")
                    return None
                    
        except Exception as e:
            print(f"❌ {symbol} 數據獲取異常: {e}")
            return None
    
    def generate_test_signals(self, df: pd.DataFrame, num_signals: int = 20) -> List[Tuple[int, str]]:
        """生成測試信號"""
        signals = []
        
        # 計算RSI用於信號生成
        rsi = self.calculator.indicators.rsi(df['close'], period=14)
        
        # 從有足夠數據的地方開始
        start_idx = 50
        end_idx = len(df) - 50  # 留出執行交易的空間
        
        signal_count = 0
        for i in range(start_idx, end_idx):
            if signal_count >= num_signals:
                break
                
            current_rsi = rsi.iloc[i]
            if pd.isna(current_rsi):
                continue
            
            # 簡單的RSI信號
            if current_rsi >= 70:  # 超買，可能反轉做空
                signals.append((i, 'SHORT'))
                signal_count += 1
            elif current_rsi <= 30:  # 超賣，可能反轉做多
                signals.append((i, 'LONG'))
                signal_count += 1
        
        return signals
    
    def simulate_trade(self, df: pd.DataFrame, entry_idx: int, direction: str,
                      stop_loss_result: StopLossResult) -> Dict:
        """模擬交易"""
        entry_price = df['close'].iloc[entry_idx]
        stop_loss_price = stop_loss_result.stop_loss_price
        take_profit_price = stop_loss_result.take_profit_price
        
        # 檢查止盈止損價格是否有效
        if pd.isna(stop_loss_price) or pd.isna(take_profit_price):
            return None
        
        # 從入場後開始檢查
        for i in range(entry_idx + 1, min(entry_idx + 100, len(df))):  # 最多檢查100個週期
            current_high = df['high'].iloc[i]
            current_low = df['low'].iloc[i]
            
            if direction == 'LONG':
                if current_low <= stop_loss_price:
                    pnl_pct = ((stop_loss_price - entry_price) / entry_price) * 100
                    return {
                        'exit_type': 'STOP_LOSS',
                        'pnl_pct': pnl_pct,
                        'holding_periods': i - entry_idx
                    }
                elif current_high >= take_profit_price:
                    pnl_pct = ((take_profit_price - entry_price) / entry_price) * 100
                    return {
                        'exit_type': 'TAKE_PROFIT',
                        'pnl_pct': pnl_pct,
                        'holding_periods': i - entry_idx
                    }
            else:  # SHORT
                if current_high >= stop_loss_price:
                    pnl_pct = ((entry_price - stop_loss_price) / entry_price) * 100
                    return {
                        'exit_type': 'STOP_LOSS',
                        'pnl_pct': pnl_pct,
                        'holding_periods': i - entry_idx
                    }
                elif current_low <= take_profit_price:
                    pnl_pct = ((entry_price - take_profit_price) / entry_price) * 100
                    return {
                        'exit_type': 'TAKE_PROFIT',
                        'pnl_pct': pnl_pct,
                        'holding_periods': i - entry_idx
                    }
        
        # 超時平倉
        final_price = df['close'].iloc[min(entry_idx + 100, len(df) - 1)]
        if direction == 'LONG':
            pnl_pct = ((final_price - entry_price) / entry_price) * 100
        else:
            pnl_pct = ((entry_price - final_price) / entry_price) * 100
        
        return {
            'exit_type': 'TIMEOUT',
            'pnl_pct': pnl_pct,
            'holding_periods': 100
        }
    
    async def test_symbol(self, symbol: str, timeframe: str = "1h") -> Optional[pd.DataFrame]:
        """測試單個幣種"""
        print(f"\n🧪 測試 {symbol} {timeframe}")
        
        # 獲取數據
        df = await self.get_data(symbol, timeframe)
        if df is None:
            return None
        
        # 生成信號
        signals = self.generate_test_signals(df, num_signals=15)
        if not signals:
            print(f"❌ {symbol} 沒有生成信號")
            return None
        
        print(f"📊 {symbol} 生成 {len(signals)} 個信號")
        
        results = []
        
        for entry_idx, direction in signals:
            entry_price = df['close'].iloc[entry_idx]
            data_slice = df.iloc[:entry_idx+1].copy()
            
            # 獲取所有止盈止損方法
            methods = self.calculator.get_all_stop_loss_methods(
                data_slice, direction, entry_price
            )
            
            if not methods:
                continue
            
            # 測試每種方法
            for method_result in methods:
                trade_result = self.simulate_trade(df, entry_idx, direction, method_result)
                
                if trade_result:
                    result = {
                        'symbol': symbol,
                        'timeframe': timeframe,
                        'direction': direction,
                        'entry_price': entry_price,
                        'method': method_result.method.value,
                        'risk_reward_ratio': method_result.risk_reward_ratio,
                        'confidence': method_result.confidence,
                        **trade_result
                    }
                    results.append(result)
        
        if results:
            print(f"✅ {symbol} 完成 {len(results)} 筆交易測試")
            return pd.DataFrame(results)
        else:
            print(f"❌ {symbol} 沒有有效結果")
            return None
    
    def analyze_results(self, results_df: pd.DataFrame):
        """分析結果"""
        print("\n" + "="*80)
        print("📈 止盈止損方法回測結果分析")
        print("="*80)
        
        # 總體統計
        total_trades = len(results_df)
        winning_trades = len(results_df[results_df['pnl_pct'] > 0])
        win_rate = winning_trades / total_trades
        avg_pnl = results_df['pnl_pct'].mean()
        
        print(f"\n📊 總體統計:")
        print(f"   總交易數: {total_trades}")
        print(f"   勝率: {win_rate:.2%}")
        print(f"   平均盈虧: {avg_pnl:.2f}%")
        print(f"   總回報: {results_df['pnl_pct'].sum():.2f}%")
        print(f"   最大盈利: {results_df['pnl_pct'].max():.2f}%")
        print(f"   最大虧損: {results_df['pnl_pct'].min():.2f}%")
        
        # 按方法分析
        print(f"\n🔍 方法比較:")
        print("-" * 80)
        
        method_stats = []
        for method in results_df['method'].unique():
            method_data = results_df[results_df['method'] == method]
            
            method_total = len(method_data)
            method_wins = len(method_data[method_data['pnl_pct'] > 0])
            method_win_rate = method_wins / method_total
            
            avg_win = method_data[method_data['pnl_pct'] > 0]['pnl_pct'].mean()
            avg_loss = method_data[method_data['pnl_pct'] < 0]['pnl_pct'].mean()
            
            tp_rate = len(method_data[method_data['exit_type'] == 'TAKE_PROFIT']) / method_total
            
            method_stats.append({
                '方法': method,
                '交易數': method_total,
                '勝率': f"{method_win_rate:.1%}",
                '平均盈虧': f"{method_data['pnl_pct'].mean():.2f}%",
                '總回報': f"{method_data['pnl_pct'].sum():.2f}%",
                '平均盈利': f"{avg_win:.2f}%" if not pd.isna(avg_win) else "N/A",
                '平均虧損': f"{avg_loss:.2f}%" if not pd.isna(avg_loss) else "N/A",
                '止盈率': f"{tp_rate:.1%}",
                '風險回報比': f"{method_data['risk_reward_ratio'].mean():.2f}"
            })
        
        method_df = pd.DataFrame(method_stats)
        print(method_df.to_string(index=False))
        
        # 按幣種分析
        print(f"\n💰 幣種表現:")
        print("-" * 50)
        
        symbol_stats = []
        for symbol in results_df['symbol'].unique():
            symbol_data = results_df[results_df['symbol'] == symbol]
            
            symbol_total = len(symbol_data)
            symbol_wins = len(symbol_data[symbol_data['pnl_pct'] > 0])
            symbol_win_rate = symbol_wins / symbol_total
            
            symbol_stats.append({
                '幣種': symbol,
                '交易數': symbol_total,
                '勝率': f"{symbol_win_rate:.1%}",
                '平均盈虧': f"{symbol_data['pnl_pct'].mean():.2f}%",
                '總回報': f"{symbol_data['pnl_pct'].sum():.2f}%"
            })
        
        symbol_df = pd.DataFrame(symbol_stats)
        print(symbol_df.to_string(index=False))
        
        # 推薦最佳方法
        best_method_by_winrate = method_df.loc[method_df['勝率'].str.rstrip('%').astype(float).idxmax()]
        best_method_by_return = method_df.loc[method_df['總回報'].str.rstrip('%').astype(float).idxmax()]
        
        print(f"\n🏆 推薦結果:")
        print(f"🥇 最高勝率: {best_method_by_winrate['方法']} ({best_method_by_winrate['勝率']})")
        print(f"💰 最高總回報: {best_method_by_return['方法']} ({best_method_by_return['總回報']})")

async def main():
    """主函數"""
    print("🎯 簡化版止盈止損方法回測")
    print("=" * 50)
    
    symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT']  # 先測試3個主要幣種
    
    async with SimpleBacktester() as backtester:
        all_results = []
        
        for symbol in symbols:
            result_df = await backtester.test_symbol(symbol, "1h")
            if result_df is not None:
                all_results.append(result_df)
            
            await asyncio.sleep(1)  # 避免請求過快
        
        if all_results:
            combined_results = pd.concat(all_results, ignore_index=True)
            
            # 保存結果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_file = f"simple_backtest_results_{timestamp}.csv"
            combined_results.to_csv(results_file, index=False)
            print(f"\n📊 結果已保存: {results_file}")
            
            # 分析結果
            backtester.analyze_results(combined_results)
            
        else:
            print("\n❌ 沒有獲得任何測試結果")

if __name__ == "__main__":
    asyncio.run(main())
