#!/bin/bash

# OBV+TI+BB策略實盤交易系統雲端部署腳本

echo "🚀 開始部署OBV+TI+BB策略實盤交易系統到雲端..."

# 檢查必要文件
echo "📋 檢查必要文件..."
required_files=("obv_ti_bb_live_trading_system.py" "Dockerfile" "docker-compose.yml" "requirements.txt")

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少必要文件: $file"
        exit 1
    fi
done

echo "✅ 所有必要文件檢查完成"

# 檢查環境變量配置
if [ ! -f ".env" ]; then
    echo "⚠️ 未找到.env文件，請先配置環境變量"
    echo "📝 請複製.env.example為.env並填入實際配置"
    cp .env.example .env
    echo "🔧 已創建.env模板文件，請編輯後重新運行部署腳本"
    exit 1
fi

# 檢查Docker是否安裝
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安裝，請先安裝Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安裝，請先安裝Docker Compose"
    exit 1
fi

echo "✅ Docker環境檢查完成"

# 創建必要目錄
echo "📁 創建必要目錄..."
mkdir -p logs data config

# 停止現有容器
echo "🛑 停止現有容器..."
docker-compose down

# 構建新鏡像
echo "🔨 構建Docker鏡像..."
docker-compose build --no-cache

# 啟動服務
echo "🚀 啟動交易系統..."
docker-compose up -d

# 檢查服務狀態
echo "📊 檢查服務狀態..."
sleep 10
docker-compose ps

# 顯示日誌
echo "📋 顯示系統日誌..."
docker-compose logs --tail=50 obv-trading-system

echo ""
echo "🎉 OBV+TI+BB策略實盤交易系統部署完成!"
echo ""
echo "📊 系統信息:"
echo "   - 容器名稱: obv_ti_bb_trading"
echo "   - 日誌目錄: ./logs"
echo "   - 數據目錄: ./data"
echo "   - 配置目錄: ./config"
echo ""
echo "🔧 管理命令:"
echo "   查看日誌: docker-compose logs -f obv-trading-system"
echo "   重啟系統: docker-compose restart obv-trading-system"
echo "   停止系統: docker-compose down"
echo "   查看狀態: docker-compose ps"
echo ""
echo "📱 請確保已正確配置Telegram Bot Token和Chat ID"
echo "⚠️ 系統將在60秒間隔內持續掃描交易信號"
