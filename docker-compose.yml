version: '3.8'

services:
  signal-system:
    build: .
    container_name: multi-strategy-signals
    restart: unless-stopped
    environment:
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
      - BLAVE_API_KEY=${BLAVE_API_KEY}
      - BLAVE_SECRET_KEY=${BLAVE_SECRET_KEY}
      - BYBIT_API_KEY=${BYBIT_API_KEY}
      - BYBIT_SECRET_KEY=${BYBIT_SECRET_KEY}
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./config:/app/config
    networks:
      - signal-network

networks:
  signal-network:
    driver: bridge
