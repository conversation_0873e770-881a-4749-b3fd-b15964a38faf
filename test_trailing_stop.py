#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
動態止損系統測試腳本
測試追蹤止損功能的各種場景
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import MainSignalSystem
from datetime import datetime
import asyncio

def test_trailing_stop_calculation():
    """測試動態止損計算邏輯"""
    print("🧪 測試動態止損計算邏輯")
    print("=" * 60)
    
    # 創建系統實例
    system = MainSignalSystem()
    
    # 測試場景1: LONG交易達到40%進展
    print("\n1️⃣ 測試LONG交易 - 40%進展觸發保本線")
    long_trade = {
        'trade_id': 'TEST_LONG_001',
        'symbol': 'BTCUSDT',
        'direction': 'LONG',
        'entry_price': '100000.0',
        'stop_loss_price': '98000.0',  # -2%
        'take_profit_price': '106000.0',  # +6%
        'trailing_stop_level': '0'
    }
    
    # 當前價格達到40%進展: 100000 + (106000-100000)*0.4 = 102400
    current_price_40pct = 102400.0
    result = system.calculate_trailing_stop(long_trade, current_price_40pct)
    
    if result:
        print(f"✅ 觸發調整: {result['trigger_type']}")
        print(f"   新止損價格: ${result['new_stop_loss']:.2f}")
        print(f"   利潤進展: {result['profit_progress_pct']:.1f}%")
        print(f"   調整原因: {result['reason']}")
    else:
        print("❌ 未觸發調整")
    
    # 測試場景2: SHORT交易達到70%進展
    print("\n2️⃣ 測試SHORT交易 - 70%進展觸發利潤鎖定")
    short_trade = {
        'trade_id': 'TEST_SHORT_001',
        'symbol': 'ETHUSDT',
        'direction': 'SHORT',
        'entry_price': '4000.0',
        'stop_loss_price': '4080.0',  # +2%
        'take_profit_price': '3760.0',  # -6%
        'trailing_stop_level': '1'  # 已經調整過一次
    }
    
    # 當前價格達到70%進展: 4000 - (4000-3760)*0.7 = 3832
    current_price_70pct = 3832.0
    result = system.calculate_trailing_stop(short_trade, current_price_70pct)
    
    if result:
        print(f"✅ 觸發調整: {result['trigger_type']}")
        print(f"   新止損價格: ${result['new_stop_loss']:.2f}")
        print(f"   利潤進展: {result['profit_progress_pct']:.1f}%")
        print(f"   調整原因: {result['reason']}")
    else:
        print("❌ 未觸發調整")
    
    # 測試場景3: 不滿足觸發條件
    print("\n3️⃣ 測試不滿足觸發條件的情況")
    current_price_low = 101000.0  # 只有16.7%進展
    result = system.calculate_trailing_stop(long_trade, current_price_low)
    
    if result:
        print(f"❌ 意外觸發: {result}")
    else:
        print("✅ 正確：未觸發調整（進展不足40%）")
    
    print("\n" + "=" * 60)

def test_csv_logging():
    """測試CSV記錄功能"""
    print("🧪 測試CSV記錄功能")
    print("=" * 60)
    
    system = MainSignalSystem()
    
    # 測試追蹤止損日誌記錄
    test_adjustment = {
        'new_stop_loss': 100500.0,
        'trigger_type': 'BREAKEVEN',
        'reason': '達到止盈目標40%，移動到保本線',
        'profit_progress_pct': 42.5,
        'trailing_stop_level': 1
    }
    
    system.log_trailing_stop_adjustment(
        'TEST_TRADE_001',
        test_adjustment,
        102400.0,  # current_price
        98000.0    # old_stop_loss
    )
    
    print("✅ 追蹤止損日誌記錄測試完成")
    print("💡 請檢查 data/trailing_stops_log.csv 文件")
    
    print("\n" + "=" * 60)

async def test_telegram_notification():
    """測試Telegram通知功能"""
    print("🧪 測試Telegram通知功能")
    print("=" * 60)
    
    system = MainSignalSystem()
    
    test_trade = {
        'trade_id': 'TEST_NOTIFICATION_001',
        'symbol': 'BTCUSDT',
        'timeframe': '1H',
        'direction': 'LONG'
    }
    
    test_adjustment = {
        'trigger_type': 'BREAKEVEN',
        'reason': '達到止盈目標40%，移動到保本線',
        'profit_progress_pct': 42.5
    }
    
    try:
        await system.send_trailing_stop_notification(
            test_trade,
            test_adjustment,
            102400.0,  # current_price
            98000.0,   # old_stop_loss
            100500.0   # new_stop_loss
        )
        print("✅ Telegram通知測試完成")
    except Exception as e:
        print(f"⚠️ Telegram通知測試失敗: {e}")
        print("💡 請檢查Telegram配置")
    
    print("\n" + "=" * 60)

def simulate_trading_scenario():
    """模擬完整的交易場景"""
    print("🎯 模擬完整交易場景")
    print("=" * 60)
    
    system = MainSignalSystem()
    
    # 模擬一個LONG交易的完整生命週期
    print("📈 模擬LONG交易生命週期:")
    
    trade = {
        'trade_id': 'SIM_BTCUSDT_LONG_001',
        'symbol': 'BTCUSDT',
        'direction': 'LONG',
        'entry_price': '100000.0',
        'stop_loss_price': '98000.0',
        'take_profit_price': '106000.0',
        'trailing_stop_level': '0'
    }
    
    # 價格變化序列
    price_sequence = [
        (100500, "入場後小幅上漲"),
        (102000, "繼續上漲，接近40%觸發點"),
        (102500, "達到40%進展，應觸發保本線調整"),
        (104000, "繼續上漲"),
        (104500, "達到70%進展，應觸發利潤鎖定"),
        (103000, "回調，測試新止損"),
        (105800, "接近止盈目標"),
        (105000, "最終回調但仍盈利")
    ]
    
    for price, description in price_sequence:
        print(f"\n💰 價格: ${price} - {description}")
        
        result = system.calculate_trailing_stop(trade, price)
        if result:
            print(f"   🎯 觸發調整: {result['trigger_type']}")
            print(f"   📊 新止損: ${result['new_stop_loss']:.2f}")
            print(f"   📈 進展: {result['profit_progress_pct']:.1f}%")
            
            # 更新交易狀態
            trade['stop_loss_price'] = str(result['new_stop_loss'])
            trade['trailing_stop_level'] = str(result['trailing_stop_level'])
        else:
            print("   ⏸️ 無調整")
        
        # 檢查是否觸發止損或止盈
        if price <= float(trade['stop_loss_price']):
            pnl = ((price - 100000) / 100000) * 100
            print(f"   🛑 觸發止損! 盈虧: {pnl:+.2f}%")
            break
        elif price >= 106000:
            print(f"   🎯 觸發止盈! 盈虧: *****%")
            break
    
    print("\n" + "=" * 60)

def main():
    """主測試函數"""
    print("🚀 動態止損系統測試開始")
    print("=" * 80)
    
    # 運行所有測試
    test_trailing_stop_calculation()
    test_csv_logging()
    
    # 異步測試
    print("🔄 運行異步測試...")
    asyncio.run(test_telegram_notification())
    
    simulate_trading_scenario()
    
    print("✅ 所有測試完成!")
    print("\n💡 建議:")
    print("1. 檢查 data/trailing_stops_log.csv 文件")
    print("2. 運行 python view_trailing_stops.py 查看記錄")
    print("3. 在實際交易前進行更多測試")

if __name__ == "__main__":
    main()
