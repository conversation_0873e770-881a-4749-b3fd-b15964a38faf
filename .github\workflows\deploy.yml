name: Deploy Signal System

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v3
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Test imports
      run: |
        python -c "import src.config_manager; import src.data_fetcher; print('✅ All imports successful')"

  deploy:
    needs: test
    runs-on: ubuntu-latest
    container: ghcr.io/railwayapp/cli:latest
    if: github.ref == 'refs/heads/main'
    env:
      RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}

    steps:
    - uses: actions/checkout@v3

    - name: Deploy to Railway
      run: railway up
