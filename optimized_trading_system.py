#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
優化版交易系統
基於回測結果，整合最佳止盈止損方法到現有交易系統
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os
import csv
from typing import Dict, List, Optional, Tuple
import logging

from stop_loss_take_profit_research import StopLossTakeProfitCalculator, StopLossResult, StopLossType

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OptimizedTradingSystem:
    """優化版交易系統"""
    
    def __init__(self):
        self.calculator = StopLossTakeProfitCalculator()
        self.session = None
        
        # 基於回測結果的最佳配置
        self.best_config = {
            'primary_method': StopLossType.SUPERTREND,  # 最佳方法
            'fallback_method': StopLossType.VWAP,       # 備用方法
            'preferred_symbols': [                       # 優選幣種
                'SOLUSDT', 'ADAUSDT', 'DOTUSDT', '1000PEPEUSDT', 
                'UNIUSDT', 'BNBUSDT', 'ETHUSDT', 'LINKUSDT'
            ],
            'preferred_timeframe': '1h',                 # 最佳時間框架
            'min_confidence': 0.7,                      # 最小信心度
            'min_risk_reward': 2.0,                     # 最小風險回報比
            'max_position_risk': 0.01,                  # 最大單筆風險 1%
        }
        
        # 策略參數
        self.rsi_period = 14
        self.rsi_long_threshold = 70
        self.rsi_short_threshold = 30
        self.ti_lookback = 24
        self.confidence_level = 0.70
        
        # Blave API配置
        self.blave_base_url = "https://api.blave.org"
        self.blave_api_key = "acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6"
        
        # 文件路徑
        self.signals_csv_path = "data/optimized_signals.csv"
        self.trades_csv_path = "data/optimized_trades.csv"
        
        # 確保目錄存在
        os.makedirs("data", exist_ok=True)
        self._init_csv_files()
    
    def _init_csv_files(self):
        """初始化CSV文件"""
        # 信號文件
        if not os.path.exists(self.signals_csv_path):
            with open(self.signals_csv_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow([
                    'timestamp', 'symbol', 'timeframe', 'direction', 'entry_price',
                    'stop_loss_price', 'take_profit_price', 'method', 'confidence',
                    'risk_reward_ratio', 'market_condition', 'rsi', 'signal_strength'
                ])
        
        # 交易記錄文件
        if not os.path.exists(self.trades_csv_path):
            with open(self.trades_csv_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow([
                    'trade_id', 'symbol', 'timeframe', 'direction', 'entry_price',
                    'stop_loss_price', 'take_profit_price', 'entry_time', 'exit_time',
                    'exit_price', 'exit_type', 'pnl_pct', 'method', 'status'
                ])
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_market_data(self, symbol: str, timeframe: str = "1h", limit: int = 200) -> Optional[pd.DataFrame]:
        """獲取市場數據"""
        try:
            interval_map = {'1h': '60', '4h': '240'}
            interval = interval_map.get(timeframe, '60')
            
            url = "https://api.bybit.com/v5/market/kline"
            params = {
                "category": "linear",
                "symbol": symbol,
                "interval": interval,
                "limit": limit
            }
            
            async with self.session.get(url, params=params, timeout=30) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("retCode") == 0:
                        kline_data = data.get("result", {}).get("list", [])
                        
                        if kline_data and len(kline_data) >= 100:
                            df = pd.DataFrame(kline_data)
                            df.columns = ["timestamp", "open", "high", "low", "close", "volume", "turnover"]
                            
                            for col in ["open", "high", "low", "close", "volume"]:
                                df[col] = pd.to_numeric(df[col])
                            
                            df["timestamp"] = pd.to_datetime(df["timestamp"].astype(float), unit="ms")
                            df = df.sort_values("timestamp").reset_index(drop=True)
                            df.set_index("timestamp", inplace=True)
                            
                            return df
                        
            return None
                    
        except Exception as e:
            logger.error(f"獲取 {symbol} 市場數據失敗: {e}")
            return None
    
    def detect_market_condition(self, df: pd.DataFrame, lookback: int = 50) -> str:
        """檢測市場條件"""
        if len(df) < lookback:
            return "normal"
        
        recent_data = df.tail(lookback)
        
        # 計算趨勢強度
        ema_20 = self.calculator.indicators.ema(recent_data['close'], 20)
        ema_50 = self.calculator.indicators.ema(recent_data['close'], 50)
        
        trend_strength = abs(ema_20.iloc[-1] - ema_50.iloc[-1]) / ema_50.iloc[-1]
        
        # 計算波動率
        atr = self.calculator.indicators.atr(recent_data['high'], recent_data['low'], recent_data['close'])
        volatility = atr.iloc[-1] / recent_data['close'].iloc[-1]
        
        # 判斷市場條件
        if trend_strength > 0.05:
            return "trending"
        elif volatility > 0.03:
            return "volatile"
        else:
            return "normal"  # 基於回測結果，normal條件表現最佳
    
    def get_optimized_stop_loss(self, df: pd.DataFrame, direction: str, entry_price: float,
                               market_condition: str = "normal") -> Optional[StopLossResult]:
        """獲取優化的止盈止損"""
        # 獲取所有方法
        methods = self.calculator.get_all_stop_loss_methods(df, direction, entry_price)
        
        if not methods:
            return None
        
        # 優先使用SUPERTREND（回測表現最佳）
        for method in methods:
            if (method.method == self.best_config['primary_method'] and 
                method.confidence >= self.best_config['min_confidence'] and
                method.risk_reward_ratio >= self.best_config['min_risk_reward']):
                return method
        
        # 如果SUPERTREND不可用，使用VWAP作為備用
        for method in methods:
            if (method.method == self.best_config['fallback_method'] and 
                method.confidence >= self.best_config['min_confidence'] and
                method.risk_reward_ratio >= 1.5):  # 降低要求
                return method
        
        # 最後使用綜合最佳方法
        return self.calculator.recommend_best_method(methods, preference="balanced")
    
    def generate_trading_signal(self, df: pd.DataFrame, symbol: str, timeframe: str) -> Optional[Dict]:
        """生成交易信號"""
        if len(df) < 100:
            return None
        
        # 計算技術指標
        rsi = self.calculator.indicators.rsi(df['close'], period=self.rsi_period)
        bb_upper, bb_middle, bb_lower = self.calculator.indicators.bollinger_bands(df['close'])
        atr = self.calculator.indicators.atr(df['high'], df['low'], df['close'])
        
        # 獲取最新數據
        current_rsi = rsi.iloc[-1]
        current_price = df['close'].iloc[-1]
        current_volume = df['volume'].iloc[-1]
        avg_volume = df['volume'].tail(10).mean()
        
        # 跳過無效數據
        if pd.isna(current_rsi) or pd.isna(bb_upper.iloc[-1]):
            return None
        
        # 檢測市場條件
        market_condition = self.detect_market_condition(df)
        
        # 信號條件檢查（5選4邏輯）
        direction = None
        signal_strength = 0
        
        # 多頭條件
        long_conditions = [
            current_rsi >= self.rsi_long_threshold,      # RSI超買
            current_price > bb_upper.iloc[-1],           # 突破布林帶上軌
            current_volume > avg_volume * 1.2,           # 成交量放大
            atr.iloc[-1] > atr.tail(10).mean(),         # 波動率增加
            market_condition == "normal"                 # 最佳市場條件
        ]
        
        # 空頭條件
        short_conditions = [
            current_rsi <= self.rsi_short_threshold,     # RSI超賣
            current_price < bb_lower.iloc[-1],           # 跌破布林帶下軌
            current_volume > avg_volume * 1.2,           # 成交量放大
            atr.iloc[-1] > atr.tail(10).mean(),         # 波動率增加
            market_condition == "normal"                 # 最佳市場條件
        ]
        
        long_score = sum(long_conditions)
        short_score = sum(short_conditions)
        
        if long_score >= 4:
            direction = 'LONG'
            signal_strength = long_score
        elif short_score >= 4:
            direction = 'SHORT'
            signal_strength = short_score
        else:
            return None
        
        # 獲取優化的止盈止損
        stop_loss_result = self.get_optimized_stop_loss(df, direction, current_price, market_condition)
        
        if not stop_loss_result:
            return None
        
        # 檢查風險回報比
        if stop_loss_result.risk_reward_ratio < self.best_config['min_risk_reward']:
            return None
        
        # 生成信號
        signal = {
            'timestamp': datetime.now().isoformat(),
            'symbol': symbol,
            'timeframe': timeframe,
            'direction': direction,
            'entry_price': current_price,
            'stop_loss_price': stop_loss_result.stop_loss_price,
            'take_profit_price': stop_loss_result.take_profit_price,
            'method': stop_loss_result.method.value,
            'confidence': stop_loss_result.confidence,
            'risk_reward_ratio': stop_loss_result.risk_reward_ratio,
            'market_condition': market_condition,
            'rsi': current_rsi,
            'signal_strength': signal_strength
        }
        
        return signal
    
    def save_signal(self, signal: Dict):
        """保存信號到CSV"""
        try:
            with open(self.signals_csv_path, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow([
                    signal['timestamp'], signal['symbol'], signal['timeframe'],
                    signal['direction'], signal['entry_price'], signal['stop_loss_price'],
                    signal['take_profit_price'], signal['method'], signal['confidence'],
                    signal['risk_reward_ratio'], signal['market_condition'],
                    signal['rsi'], signal['signal_strength']
                ])
            logger.info(f"✅ 信號已保存: {signal['symbol']} {signal['direction']}")
        except Exception as e:
            logger.error(f"保存信號失敗: {e}")
    
    async def scan_for_signals(self) -> List[Dict]:
        """掃描所有優選幣種尋找信號"""
        signals = []
        
        logger.info("🔍 開始掃描交易信號...")
        
        for symbol in self.best_config['preferred_symbols']:
            try:
                # 獲取市場數據
                df = await self.get_market_data(symbol, self.best_config['preferred_timeframe'])
                
                if df is not None:
                    # 生成信號
                    signal = self.generate_trading_signal(df, symbol, self.best_config['preferred_timeframe'])
                    
                    if signal:
                        signals.append(signal)
                        self.save_signal(signal)
                        logger.info(f"🎯 發現信號: {symbol} {signal['direction']} (方法: {signal['method']})")
                
                # 避免請求過快
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"掃描 {symbol} 失敗: {e}")
                continue
        
        logger.info(f"📊 掃描完成，發現 {len(signals)} 個信號")
        return signals

    async def run_signal_generation(self, duration_minutes: int = 60):
        """運行信號生成（測試模式）"""
        logger.info(f"🚀 開始優化版信號生成系統")
        logger.info(f"⏰ 運行時長: {duration_minutes} 分鐘")
        logger.info(f"📊 監控幣種: {self.best_config['preferred_symbols']}")
        logger.info(f"🔍 使用方法: {self.best_config['primary_method'].value} (主) + {self.best_config['fallback_method'].value} (備)")
        logger.info("=" * 80)

        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)

        total_signals = 0

        while datetime.now() < end_time:
            try:
                # 掃描信號
                signals = await self.scan_for_signals()
                total_signals += len(signals)

                if signals:
                    print(f"\n🎯 {datetime.now().strftime('%H:%M:%S')} - 發現 {len(signals)} 個新信號:")
                    for signal in signals:
                        print(f"   📈 {signal['symbol']} {signal['direction']} @ ${signal['entry_price']:.4f}")
                        print(f"      止損: ${signal['stop_loss_price']:.4f} | 止盈: ${signal['take_profit_price']:.4f}")
                        print(f"      方法: {signal['method']} | 風險回報比: {signal['risk_reward_ratio']:.2f}")

                # 等待下一次掃描
                await asyncio.sleep(60)  # 每分鐘掃描一次

            except Exception as e:
                logger.error(f"信號生成過程中出錯: {e}")
                await asyncio.sleep(30)

        logger.info(f"✅ 信號生成完成，總共生成 {total_signals} 個信號")
        return total_signals

    def analyze_recent_signals(self, hours: int = 24) -> Dict:
        """分析最近的信號表現"""
        try:
            # 讀取信號文件
            df = pd.read_csv(self.signals_csv_path)

            if df.empty:
                return {'error': '沒有信號數據'}

            # 過濾最近的信號
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            cutoff_time = datetime.now() - timedelta(hours=hours)
            recent_signals = df[df['timestamp'] >= cutoff_time]

            if recent_signals.empty:
                return {'error': f'最近{hours}小時沒有信號'}

            # 統計分析
            analysis = {
                'total_signals': len(recent_signals),
                'by_symbol': recent_signals['symbol'].value_counts().to_dict(),
                'by_direction': recent_signals['direction'].value_counts().to_dict(),
                'by_method': recent_signals['method'].value_counts().to_dict(),
                'avg_confidence': recent_signals['confidence'].mean(),
                'avg_risk_reward': recent_signals['risk_reward_ratio'].mean(),
                'avg_signal_strength': recent_signals['signal_strength'].mean(),
                'time_range': {
                    'start': recent_signals['timestamp'].min().isoformat(),
                    'end': recent_signals['timestamp'].max().isoformat()
                }
            }

            return analysis

        except Exception as e:
            logger.error(f"分析信號失敗: {e}")
            return {'error': str(e)}

    def print_system_status(self):
        """打印系統狀態"""
        print("\n" + "="*80)
        print("📊 優化版交易系統狀態")
        print("="*80)

        print(f"🎯 最佳配置:")
        print(f"   主要方法: {self.best_config['primary_method'].value}")
        print(f"   備用方法: {self.best_config['fallback_method'].value}")
        print(f"   時間框架: {self.best_config['preferred_timeframe']}")
        print(f"   最小風險回報比: {self.best_config['min_risk_reward']}")
        print(f"   最小信心度: {self.best_config['min_confidence']}")

        print(f"\n💰 優選幣種 ({len(self.best_config['preferred_symbols'])} 個):")
        for i, symbol in enumerate(self.best_config['preferred_symbols'], 1):
            print(f"   {i:2d}. {symbol}")

        # 分析最近信號
        analysis = self.analyze_recent_signals(24)

        if 'error' not in analysis:
            print(f"\n📈 最近24小時信號統計:")
            print(f"   總信號數: {analysis['total_signals']}")
            print(f"   平均信心度: {analysis['avg_confidence']:.3f}")
            print(f"   平均風險回報比: {analysis['avg_risk_reward']:.2f}")
            print(f"   平均信號強度: {analysis['avg_signal_strength']:.1f}")

            if analysis['by_method']:
                print(f"   方法分布: {analysis['by_method']}")

            if analysis['by_direction']:
                print(f"   方向分布: {analysis['by_direction']}")
        else:
            print(f"\n⚠️ 信號分析: {analysis['error']}")

async def main():
    """主函數"""
    print("🎯 優化版交易系統")
    print("基於852筆交易回測結果的最佳配置")
    print("=" * 60)

    async with OptimizedTradingSystem() as system:
        # 顯示系統狀態
        system.print_system_status()

        # 選擇運行模式
        print(f"\n🔧 運行模式:")
        print("1. 單次信號掃描")
        print("2. 持續信號生成 (測試模式)")
        print("3. 信號分析")

        try:
            choice = input("\n請選擇模式 (1-3): ").strip()

            if choice == "1":
                # 單次掃描
                signals = await system.scan_for_signals()
                if signals:
                    print(f"\n🎯 發現 {len(signals)} 個交易信號:")
                    for signal in signals:
                        print(f"📈 {signal['symbol']} {signal['direction']} @ ${signal['entry_price']:.4f}")
                        print(f"   止損: ${signal['stop_loss_price']:.4f} | 止盈: ${signal['take_profit_price']:.4f}")
                        print(f"   方法: {signal['method']} | 風險回報比: {signal['risk_reward_ratio']:.2f}")
                else:
                    print("📊 當前沒有符合條件的交易信號")

            elif choice == "2":
                # 持續生成
                duration = int(input("請輸入運行時長(分鐘，默認60): ") or "60")
                await system.run_signal_generation(duration)

            elif choice == "3":
                # 信號分析
                hours = int(input("請輸入分析時間範圍(小時，默認24): ") or "24")
                analysis = system.analyze_recent_signals(hours)

                if 'error' not in analysis:
                    print(f"\n📊 最近{hours}小時信號分析:")
                    print(f"總信號數: {analysis['total_signals']}")
                    print(f"平均信心度: {analysis['avg_confidence']:.3f}")
                    print(f"平均風險回報比: {analysis['avg_risk_reward']:.2f}")
                    print(f"方法分布: {analysis['by_method']}")
                    print(f"方向分布: {analysis['by_direction']}")
                else:
                    print(f"❌ {analysis['error']}")

            else:
                print("❌ 無效選擇")

        except KeyboardInterrupt:
            print("\n⏹️ 用戶中斷")
        except Exception as e:
            logger.error(f"運行錯誤: {e}")

if __name__ == "__main__":
    asyncio.run(main())
