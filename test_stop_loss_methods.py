#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
止盈止損方法測試主程序
系統化測試和比較不同止盈止損方法的效果
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime
import json
import os
from typing import Dict, List
import matplotlib.pyplot as plt
import seaborn as sns

from stop_loss_analyzer import StopLossAnalyzer

class StopLossMethodTester:
    """止盈止損方法測試器"""
    
    def __init__(self):
        self.analyzer = StopLossAnalyzer()
        self.results_dir = "stop_loss_results"
        os.makedirs(self.results_dir, exist_ok=True)
        
    async def run_comprehensive_test(self):
        """運行全面測試"""
        print("🚀 開始止盈止損方法綜合測試")
        print("=" * 60)
        
        all_results = []
        
        # 測試所有幣種和時間框架組合
        for symbol in self.analyzer.test_symbols:
            for timeframe in self.analyzer.test_timeframes:
                try:
                    result_df = await self.analyzer.test_stop_loss_methods(symbol, timeframe)
                    if result_df is not None and not result_df.empty:
                        all_results.append(result_df)
                        print(f"✅ {symbol} {timeframe} 測試完成: {len(result_df)} 筆交易")
                    else:
                        print(f"❌ {symbol} {timeframe} 測試失敗")
                        
                except Exception as e:
                    print(f"❌ {symbol} {timeframe} 測試出錯: {e}")
                    continue
        
        if not all_results:
            print("❌ 沒有獲得任何測試結果")
            return None, None
        
        # 合併所有結果
        combined_results = pd.concat(all_results, ignore_index=True)
        
        # 保存原始結果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"{self.results_dir}/stop_loss_test_results_{timestamp}.csv"
        combined_results.to_csv(results_file, index=False)
        print(f"📊 原始結果已保存: {results_file}")
        
        # 分析結果
        analysis = self.analyzer.analyze_results(combined_results)
        
        # 生成報告
        await self.generate_comprehensive_report(combined_results, analysis, timestamp)
        
        return combined_results, analysis
    
    async def generate_comprehensive_report(self, results_df: pd.DataFrame, 
                                          analysis: Dict, timestamp: str):
        """生成綜合報告"""
        print("\n📈 生成綜合分析報告...")
        
        # 1. 總體統計報告
        self.print_overall_statistics(results_df, analysis)
        
        # 2. 方法比較報告
        self.print_method_comparison(analysis)
        
        # 3. 幣種表現報告
        self.print_symbol_performance(results_df)
        
        # 4. 時間框架比較
        self.print_timeframe_comparison(results_df)
        
        # 5. 推薦最佳組合
        recommendations = self.generate_recommendations(analysis)
        self.print_recommendations(recommendations)
        
        # 6. 保存詳細報告
        report_file = f"{self.results_dir}/stop_loss_analysis_report_{timestamp}.json"
        report_data = {
            'timestamp': timestamp,
            'overall_stats': self.get_overall_stats(results_df),
            'method_analysis': analysis,
            'recommendations': recommendations
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"📄 詳細報告已保存: {report_file}")
    
    def print_overall_statistics(self, results_df: pd.DataFrame, analysis: Dict):
        """打印總體統計"""
        print("\n" + "="*60)
        print("📊 總體統計")
        print("="*60)
        
        total_trades = len(results_df)
        total_methods = len(analysis)
        
        print(f"總交易數: {total_trades}")
        print(f"測試方法數: {total_methods}")
        print(f"測試幣種: {', '.join(results_df['symbol'].unique())}")
        print(f"測試時間框架: {', '.join(results_df['timeframe'].unique())}")
        
        # 整體勝率和盈虧
        overall_win_rate = len(results_df[results_df['pnl_pct'] > 0]) / total_trades
        overall_avg_pnl = results_df['pnl_pct'].mean()
        
        print(f"\n整體勝率: {overall_win_rate:.2%}")
        print(f"平均盈虧: {overall_avg_pnl:.2f}%")
    
    def print_method_comparison(self, analysis: Dict):
        """打印方法比較"""
        print("\n" + "="*60)
        print("🔍 方法比較分析")
        print("="*60)
        
        # 創建比較表格
        comparison_data = []
        for method, stats in analysis.items():
            comparison_data.append({
                '方法': method,
                '勝率': f"{stats['win_rate']:.2%}",
                '平均盈虧': f"{stats['avg_pnl']:.2f}%",
                '平均盈利': f"{stats['avg_win']:.2f}%",
                '平均虧損': f"{stats['avg_loss']:.2f}%",
                '最大回撤': f"{stats['max_drawdown']:.2f}%",
                '止盈率': f"{stats['take_profit_rate']:.2%}",
                '風險回報比': f"{stats['avg_risk_reward']:.2f}",
                '信心度': f"{stats['avg_confidence']:.2f}"
            })
        
        comparison_df = pd.DataFrame(comparison_data)
        print(comparison_df.to_string(index=False))
    
    def print_symbol_performance(self, results_df: pd.DataFrame):
        """打印幣種表現"""
        print("\n" + "="*60)
        print("💰 幣種表現分析")
        print("="*60)
        
        symbol_stats = []
        for symbol in results_df['symbol'].unique():
            symbol_data = results_df[results_df['symbol'] == symbol]
            
            win_rate = len(symbol_data[symbol_data['pnl_pct'] > 0]) / len(symbol_data)
            avg_pnl = symbol_data['pnl_pct'].mean()
            
            symbol_stats.append({
                '幣種': symbol,
                '交易數': len(symbol_data),
                '勝率': f"{win_rate:.2%}",
                '平均盈虧': f"{avg_pnl:.2f}%"
            })
        
        symbol_df = pd.DataFrame(symbol_stats)
        print(symbol_df.to_string(index=False))
    
    def print_timeframe_comparison(self, results_df: pd.DataFrame):
        """打印時間框架比較"""
        print("\n" + "="*60)
        print("⏰ 時間框架比較")
        print("="*60)
        
        timeframe_stats = []
        for timeframe in results_df['timeframe'].unique():
            tf_data = results_df[results_df['timeframe'] == timeframe]
            
            win_rate = len(tf_data[tf_data['pnl_pct'] > 0]) / len(tf_data)
            avg_pnl = tf_data['pnl_pct'].mean()
            
            timeframe_stats.append({
                '時間框架': timeframe,
                '交易數': len(tf_data),
                '勝率': f"{win_rate:.2%}",
                '平均盈虧': f"{avg_pnl:.2f}%"
            })
        
        tf_df = pd.DataFrame(timeframe_stats)
        print(tf_df.to_string(index=False))
    
    def generate_recommendations(self, analysis: Dict) -> Dict:
        """生成推薦"""
        recommendations = {}
        
        # 按不同標準排序
        methods_by_winrate = sorted(analysis.items(), 
                                   key=lambda x: x[1]['win_rate'], reverse=True)
        methods_by_pnl = sorted(analysis.items(), 
                               key=lambda x: x[1]['avg_pnl'], reverse=True)
        methods_by_risk_reward = sorted(analysis.items(), 
                                       key=lambda x: x[1]['avg_risk_reward'], reverse=True)
        
        recommendations['highest_winrate'] = methods_by_winrate[0]
        recommendations['highest_pnl'] = methods_by_pnl[0]
        recommendations['best_risk_reward'] = methods_by_risk_reward[0]
        
        # 綜合評分推薦
        scored_methods = []
        for method, stats in analysis.items():
            # 綜合評分公式
            score = (stats['win_rate'] * 0.3 + 
                    min(stats['avg_pnl'] / 10, 1.0) * 0.3 +  # 標準化平均盈虧
                    min(stats['avg_risk_reward'] / 5, 1.0) * 0.2 +  # 標準化風險回報比
                    stats['avg_confidence'] * 0.2)
            
            scored_methods.append((method, stats, score))
        
        scored_methods.sort(key=lambda x: x[2], reverse=True)
        recommendations['overall_best'] = scored_methods[0]
        
        return recommendations
    
    def print_recommendations(self, recommendations: Dict):
        """打印推薦結果"""
        print("\n" + "="*60)
        print("🏆 推薦結果")
        print("="*60)
        
        print(f"🥇 最高勝率方法: {recommendations['highest_winrate'][0]}")
        print(f"   勝率: {recommendations['highest_winrate'][1]['win_rate']:.2%}")
        
        print(f"\n💰 最高盈利方法: {recommendations['highest_pnl'][0]}")
        print(f"   平均盈虧: {recommendations['highest_pnl'][1]['avg_pnl']:.2f}%")
        
        print(f"\n⚖️ 最佳風險回報比方法: {recommendations['best_risk_reward'][0]}")
        print(f"   風險回報比: {recommendations['best_risk_reward'][1]['avg_risk_reward']:.2f}")
        
        print(f"\n🎯 綜合最佳方法: {recommendations['overall_best'][0]}")
        print(f"   綜合評分: {recommendations['overall_best'][2]:.3f}")
        print(f"   勝率: {recommendations['overall_best'][1]['win_rate']:.2%}")
        print(f"   平均盈虧: {recommendations['overall_best'][1]['avg_pnl']:.2f}%")
        print(f"   風險回報比: {recommendations['overall_best'][1]['avg_risk_reward']:.2f}")
    
    def get_overall_stats(self, results_df: pd.DataFrame) -> Dict:
        """獲取總體統計數據"""
        return {
            'total_trades': len(results_df),
            'overall_win_rate': len(results_df[results_df['pnl_pct'] > 0]) / len(results_df),
            'overall_avg_pnl': results_df['pnl_pct'].mean(),
            'symbols_tested': list(results_df['symbol'].unique()),
            'timeframes_tested': list(results_df['timeframe'].unique()),
            'methods_tested': list(results_df['method'].unique())
        }

async def main():
    """主函數"""
    print("🎯 止盈止損方法系統化測試")
    print("=" * 60)
    
    tester = StopLossMethodTester()
    
    try:
        results, analysis = await tester.run_comprehensive_test()

        if results is not None and analysis is not None:
            print("\n✅ 測試完成！")
            print("📁 結果文件保存在 stop_loss_results/ 目錄中")
        else:
            print("\n❌ 測試失敗，無法獲取數據")

    except Exception as e:
        print(f"❌ 測試過程中出現錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
