#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改進版"擴充版RSI+多空力道策略"回測系統
整合優化的止盈止損方法，擴大測試範圍，實現動態切換機制
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os
from typing import Dict, List, Optional, Tuple
import logging

from stop_loss_take_profit_research import StopLossTakeProfitCalculator, StopLossResult, StopLossType

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedRSITIBacktester:
    """改進版RSI+多空力道策略回測器"""
    
    def __init__(self):
        self.calculator = StopLossTakeProfitCalculator()
        self.session = None
        
        # Blave API配置
        self.blave_base_url = "https://api.blave.org"
        self.blave_api_key = "acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6"
        
        # 擴大測試範圍
        self.test_symbols = [
            'BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', '1000PEPEUSDT',
            'BNBUSDT', 'ADAUSDT', 'DOGEUSDT', 'MATICUSDT', 'DOTUSDT',
            'AVAXUSDT', 'LINKUSDT', 'UNIUSDT', 'LTCUSDT', 'BCHUSDT'
        ]
        self.test_timeframes = ['1h', '4h']
        
        # 策略參數
        self.rsi_period = 14
        self.rsi_long_threshold = 70
        self.rsi_short_threshold = 30
        self.ti_lookback = 24
        self.confidence_level = 0.70
        
        # 結果存儲
        self.results_dir = "advanced_backtest_results"
        os.makedirs(self.results_dir, exist_ok=True)
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_bybit_data(self, symbol: str, timeframe: str, limit: int = 1000) -> Optional[pd.DataFrame]:
        """獲取Bybit數據"""
        try:
            interval_map = {'1h': '60', '4h': '240'}
            interval = interval_map.get(timeframe, '60')
            
            url = "https://api.bybit.com/v5/market/kline"
            params = {
                "category": "linear",
                "symbol": symbol,
                "interval": interval,
                "limit": limit
            }
            
            async with self.session.get(url, params=params, timeout=30) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("retCode") == 0:
                        kline_data = data.get("result", {}).get("list", [])
                        
                        if kline_data and len(kline_data) >= 200:
                            df = pd.DataFrame(kline_data)
                            df.columns = ["timestamp", "open", "high", "low", "close", "volume", "turnover"]
                            
                            for col in ["open", "high", "low", "close", "volume"]:
                                df[col] = pd.to_numeric(df[col])
                            
                            df["timestamp"] = pd.to_datetime(df["timestamp"].astype(float), unit="ms")
                            df = df.sort_values("timestamp").reset_index(drop=True)
                            df.set_index("timestamp", inplace=True)
                            
                            return df
                        
            return None
                    
        except Exception as e:
            logger.error(f"獲取 {symbol} {timeframe} 數據失敗: {e}")
            return None
    
    async def get_blave_taker_intensity(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """獲取Blave Taker Intensity數據"""
        try:
            # 符號映射
            symbol_map = {
                '1000PEPEUSDT': '1000PEPE',
                'BTCUSDT': 'BTC',
                'ETHUSDT': 'ETH',
                'SOLUSDT': 'SOL',
                'XRPUSDT': 'XRP',
                'BNBUSDT': 'BNB',
                'ADAUSDT': 'ADA',
                'DOGEUSDT': 'DOGE',
                'MATICUSDT': 'MATIC',
                'DOTUSDT': 'DOT',
                'AVAXUSDT': 'AVAX',
                'LINKUSDT': 'LINK',
                'UNIUSDT': 'UNI',
                'LTCUSDT': 'LTC',
                'BCHUSDT': 'BCH'
            }
            
            blave_symbol = symbol_map.get(symbol, symbol.replace('USDT', ''))
            
            # 計算時間範圍
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            
            url = f"{self.blave_base_url}/taker_intensity/get_alpha"
            params = {
                "symbol": blave_symbol,
                "period": str(self.ti_lookback),
                "start_date": start_date,
                "end_date": end_date,
                "timeframe": timeframe.lower()
            }
            
            headers = {"X-API-KEY": self.blave_api_key}
            
            async with self.session.get(url, params=params, headers=headers, timeout=30) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("success") and data.get("data"):
                        df = pd.DataFrame(data["data"])
                        df['timestamp'] = pd.to_datetime(df['timestamp'])
                        df.set_index('timestamp', inplace=True)
                        return df
                        
            return None
                    
        except Exception as e:
            logger.warning(f"獲取 {symbol} Taker Intensity數據失敗: {e}")
            return None
    
    def generate_rsi_ti_signals(self, price_df: pd.DataFrame, ti_df: Optional[pd.DataFrame] = None) -> List[Tuple[int, str, Dict]]:
        """
        生成RSI+多空力道信號（5選4邏輯）
        
        Returns:
            List of (index, direction, signal_info)
        """
        signals = []
        
        # 計算技術指標
        rsi = self.calculator.indicators.rsi(price_df['close'], period=self.rsi_period)
        bb_upper, bb_middle, bb_lower = self.calculator.indicators.bollinger_bands(price_df['close'])
        atr = self.calculator.indicators.atr(price_df['high'], price_df['low'], price_df['close'])
        
        # 從足夠的數據開始檢查
        start_idx = max(self.rsi_period, self.ti_lookback, 50)
        end_idx = len(price_df) - 50  # 留出執行交易的空間
        
        for i in range(start_idx, end_idx):
            current_time = price_df.index[i]
            current_price = price_df['close'].iloc[i]
            current_rsi = rsi.iloc[i]
            
            # 跳過無效數據
            if pd.isna(current_rsi) or pd.isna(bb_upper.iloc[i]):
                continue
            
            # 獲取Taker Intensity數據
            ti_long_signal = False
            ti_short_signal = False
            
            if ti_df is not None:
                # 找到最接近的時間點
                closest_ti_idx = ti_df.index.get_indexer([current_time], method='nearest')[0]
                if closest_ti_idx >= 0 and closest_ti_idx < len(ti_df):
                    ti_value = ti_df.iloc[closest_ti_idx].get('taker_intensity', 0)
                    
                    # 計算TI信號（基於歷史分位數）
                    ti_window = ti_df.iloc[max(0, closest_ti_idx-self.ti_lookback):closest_ti_idx+1]
                    if len(ti_window) > 10:
                        ti_percentile_70 = ti_window['taker_intensity'].quantile(self.confidence_level)
                        ti_percentile_30 = ti_window['taker_intensity'].quantile(1 - self.confidence_level)
                        
                        ti_long_signal = ti_value > ti_percentile_70
                        ti_short_signal = ti_value < ti_percentile_30
            
            # 5個條件檢查
            conditions_long = [
                current_rsi >= self.rsi_long_threshold,  # RSI超買
                current_price > bb_upper.iloc[i],        # 價格突破布林帶上軌
                price_df['volume'].iloc[i] > price_df['volume'].iloc[i-5:i].mean(),  # 成交量放大
                ti_long_signal,                          # Taker Intensity多頭信號
                atr.iloc[i] > atr.iloc[i-10:i].mean()   # 波動率增加
            ]
            
            conditions_short = [
                current_rsi <= self.rsi_short_threshold,  # RSI超賣
                current_price < bb_lower.iloc[i],         # 價格跌破布林帶下軌
                price_df['volume'].iloc[i] > price_df['volume'].iloc[i-5:i].mean(),  # 成交量放大
                ti_short_signal,                          # Taker Intensity空頭信號
                atr.iloc[i] > atr.iloc[i-10:i].mean()    # 波動率增加
            ]
            
            # 5選4邏輯
            long_score = sum(conditions_long)
            short_score = sum(conditions_short)
            
            signal_info = {
                'rsi': current_rsi,
                'price': current_price,
                'bb_upper': bb_upper.iloc[i],
                'bb_lower': bb_lower.iloc[i],
                'volume_ratio': price_df['volume'].iloc[i] / price_df['volume'].iloc[i-5:i].mean(),
                'atr_ratio': atr.iloc[i] / atr.iloc[i-10:i].mean(),
                'ti_signal': ti_long_signal if long_score >= 4 else (ti_short_signal if short_score >= 4 else None)
            }
            
            if long_score >= 4:
                signals.append((i, 'LONG', signal_info))
            elif short_score >= 4:
                signals.append((i, 'SHORT', signal_info))
        
        return signals
    
    def get_optimized_stop_loss_method(self, df: pd.DataFrame, direction: str, entry_price: float,
                                     market_condition: str = "normal") -> StopLossResult:
        """
        根據市場條件動態選擇最佳止盈止損方法
        
        Args:
            df: 價格數據
            direction: 交易方向
            entry_price: 入場價格
            market_condition: 市場條件 ("trending", "ranging", "volatile", "normal")
        """
        # 獲取所有方法
        methods = self.calculator.get_all_stop_loss_methods(df, direction, entry_price)
        
        if not methods:
            return None
        
        # 根據市場條件選擇最佳方法
        if market_condition == "trending":
            # 趨勢市場：優先選擇SUPERTREND或SAR
            preferred_methods = [StopLossType.SUPERTREND, StopLossType.SAR, StopLossType.BOLLINGER]
        elif market_condition == "ranging":
            # 震盪市場：優先選擇VWAP或支撐阻力
            preferred_methods = [StopLossType.VWAP, StopLossType.SUPPORT_RESISTANCE, StopLossType.BOLLINGER]
        elif market_condition == "volatile":
            # 高波動市場：優先選擇ATR或多指標
            preferred_methods = [StopLossType.ATR, StopLossType.MULTI_INDICATOR, StopLossType.SAR]
        else:
            # 正常市場：使用綜合評分
            return self.calculator.recommend_best_method(methods, preference="balanced")
        
        # 按偏好順序選擇
        for preferred_type in preferred_methods:
            for method in methods:
                if method.method == preferred_type and method.risk_reward_ratio > 0:
                    return method
        
        # 如果沒有找到偏好方法，返回最佳綜合方法
        return self.calculator.recommend_best_method(methods, preference="balanced")
    
    def detect_market_condition(self, df: pd.DataFrame, lookback: int = 50) -> str:
        """
        檢測市場條件
        
        Returns:
            "trending", "ranging", "volatile", "normal"
        """
        if len(df) < lookback:
            return "normal"
        
        recent_data = df.tail(lookback)
        
        # 計算趨勢強度
        ema_20 = self.calculator.indicators.ema(recent_data['close'], 20)
        ema_50 = self.calculator.indicators.ema(recent_data['close'], 50)
        
        trend_strength = abs(ema_20.iloc[-1] - ema_50.iloc[-1]) / ema_50.iloc[-1]
        
        # 計算波動率
        atr = self.calculator.indicators.atr(recent_data['high'], recent_data['low'], recent_data['close'])
        volatility = atr.iloc[-1] / recent_data['close'].iloc[-1]
        
        # 計算價格範圍
        price_range = (recent_data['high'].max() - recent_data['low'].min()) / recent_data['close'].mean()
        
        # 判斷市場條件
        if trend_strength > 0.05:
            return "trending"
        elif volatility > 0.03:
            return "volatile"
        elif price_range < 0.15:
            return "ranging"
        else:
            return "normal"

    def simulate_advanced_trade(self, df: pd.DataFrame, entry_idx: int, direction: str,
                               signal_info: Dict, market_condition: str) -> Optional[Dict]:
        """
        模擬改進版交易執行
        """
        entry_price = df['close'].iloc[entry_idx]
        entry_time = df.index[entry_idx]

        # 獲取優化的止盈止損方法
        data_slice = df.iloc[:entry_idx+1].copy()
        stop_loss_result = self.get_optimized_stop_loss_method(
            data_slice, direction, entry_price, market_condition
        )

        if not stop_loss_result or pd.isna(stop_loss_result.stop_loss_price):
            return None

        stop_loss_price = stop_loss_result.stop_loss_price
        take_profit_price = stop_loss_result.take_profit_price

        # 模擬交易執行
        max_holding_periods = 168 if df.index.freq == 'H' else 42  # 1週
        end_idx = min(entry_idx + max_holding_periods, len(df) - 1)

        for i in range(entry_idx + 1, end_idx + 1):
            current_high = df['high'].iloc[i]
            current_low = df['low'].iloc[i]
            current_time = df.index[i]

            if direction == 'LONG':
                if current_low <= stop_loss_price:
                    pnl_pct = ((stop_loss_price - entry_price) / entry_price) * 100
                    return {
                        'entry_time': entry_time,
                        'exit_time': current_time,
                        'entry_price': entry_price,
                        'exit_price': stop_loss_price,
                        'exit_type': 'STOP_LOSS',
                        'pnl_pct': pnl_pct,
                        'holding_periods': i - entry_idx,
                        'method': stop_loss_result.method.value,
                        'market_condition': market_condition,
                        'risk_reward_ratio': stop_loss_result.risk_reward_ratio,
                        'confidence': stop_loss_result.confidence,
                        **signal_info
                    }
                elif current_high >= take_profit_price:
                    pnl_pct = ((take_profit_price - entry_price) / entry_price) * 100
                    return {
                        'entry_time': entry_time,
                        'exit_time': current_time,
                        'entry_price': entry_price,
                        'exit_price': take_profit_price,
                        'exit_type': 'TAKE_PROFIT',
                        'pnl_pct': pnl_pct,
                        'holding_periods': i - entry_idx,
                        'method': stop_loss_result.method.value,
                        'market_condition': market_condition,
                        'risk_reward_ratio': stop_loss_result.risk_reward_ratio,
                        'confidence': stop_loss_result.confidence,
                        **signal_info
                    }
            else:  # SHORT
                if current_high >= stop_loss_price:
                    pnl_pct = ((entry_price - stop_loss_price) / entry_price) * 100
                    return {
                        'entry_time': entry_time,
                        'exit_time': current_time,
                        'entry_price': entry_price,
                        'exit_price': stop_loss_price,
                        'exit_type': 'STOP_LOSS',
                        'pnl_pct': pnl_pct,
                        'holding_periods': i - entry_idx,
                        'method': stop_loss_result.method.value,
                        'market_condition': market_condition,
                        'risk_reward_ratio': stop_loss_result.risk_reward_ratio,
                        'confidence': stop_loss_result.confidence,
                        **signal_info
                    }
                elif current_low <= take_profit_price:
                    pnl_pct = ((entry_price - take_profit_price) / entry_price) * 100
                    return {
                        'entry_time': entry_time,
                        'exit_time': current_time,
                        'entry_price': entry_price,
                        'exit_price': take_profit_price,
                        'exit_type': 'TAKE_PROFIT',
                        'pnl_pct': pnl_pct,
                        'holding_periods': i - entry_idx,
                        'method': stop_loss_result.method.value,
                        'market_condition': market_condition,
                        'risk_reward_ratio': stop_loss_result.risk_reward_ratio,
                        'confidence': stop_loss_result.confidence,
                        **signal_info
                    }

        # 超時平倉
        final_price = df['close'].iloc[end_idx]
        final_time = df.index[end_idx]

        if direction == 'LONG':
            pnl_pct = ((final_price - entry_price) / entry_price) * 100
        else:
            pnl_pct = ((entry_price - final_price) / entry_price) * 100

        return {
            'entry_time': entry_time,
            'exit_time': final_time,
            'entry_price': entry_price,
            'exit_price': final_price,
            'exit_type': 'TIMEOUT',
            'pnl_pct': pnl_pct,
            'holding_periods': end_idx - entry_idx,
            'method': stop_loss_result.method.value,
            'market_condition': market_condition,
            'risk_reward_ratio': stop_loss_result.risk_reward_ratio,
            'confidence': stop_loss_result.confidence,
            **signal_info
        }

    async def backtest_symbol_timeframe(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """回測單個幣種和時間框架"""
        logger.info(f"🧪 開始回測 {symbol} {timeframe}")

        # 獲取價格數據
        price_df = await self.get_bybit_data(symbol, timeframe, limit=1000)
        if price_df is None or len(price_df) < 200:
            logger.error(f"❌ {symbol} {timeframe} 價格數據不足")
            return None

        # 獲取Taker Intensity數據
        ti_df = await self.get_blave_taker_intensity(symbol, timeframe)
        if ti_df is None:
            logger.warning(f"⚠️ {symbol} {timeframe} 無Taker Intensity數據，使用純RSI策略")

        # 生成信號
        signals = self.generate_rsi_ti_signals(price_df, ti_df)
        if not signals:
            logger.warning(f"⚠️ {symbol} {timeframe} 沒有生成交易信號")
            return None

        logger.info(f"📊 {symbol} {timeframe} 生成 {len(signals)} 個交易信號")

        results = []

        for entry_idx, direction, signal_info in signals:
            # 檢測市場條件
            market_condition = self.detect_market_condition(
                price_df.iloc[:entry_idx+1], lookback=50
            )

            # 模擬交易
            trade_result = self.simulate_advanced_trade(
                price_df, entry_idx, direction, signal_info, market_condition
            )

            if trade_result:
                result = {
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'direction': direction,
                    **trade_result
                }
                results.append(result)

        if results:
            logger.info(f"✅ {symbol} {timeframe} 完成 {len(results)} 筆交易測試")
            return pd.DataFrame(results)
        else:
            logger.warning(f"⚠️ {symbol} {timeframe} 沒有有效的交易結果")
            return None

    async def run_comprehensive_backtest(self) -> Tuple[Optional[pd.DataFrame], Optional[Dict]]:
        """運行全面回測"""
        logger.info("🚀 開始改進版RSI+多空力道策略全面回測")
        logger.info("=" * 80)
        logger.info(f"📊 測試幣種: {len(self.test_symbols)} 個")
        logger.info(f"⏰ 測試時間框架: {self.test_timeframes}")
        logger.info(f"🔍 策略: RSI+Taker Intensity (5選4) + 動態止盈止損")
        logger.info("=" * 80)

        all_results = []

        for symbol in self.test_symbols:
            for timeframe in self.test_timeframes:
                try:
                    result_df = await self.backtest_symbol_timeframe(symbol, timeframe)
                    if result_df is not None and not result_df.empty:
                        all_results.append(result_df)

                except Exception as e:
                    logger.error(f"❌ {symbol} {timeframe} 回測出錯: {e}")
                    continue

                # 避免請求過於頻繁
                await asyncio.sleep(0.5)

        if not all_results:
            logger.error("❌ 沒有獲得任何回測結果")
            return None, None

        # 合併所有結果
        combined_results = pd.concat(all_results, ignore_index=True)

        # 保存原始結果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"{self.results_dir}/advanced_rsi_ti_results_{timestamp}.csv"
        combined_results.to_csv(results_file, index=False)
        logger.info(f"📊 原始結果已保存: {results_file}")

        # 分析結果
        analysis = self.analyze_advanced_results(combined_results)

        # 生成報告
        self.generate_advanced_report(combined_results, analysis, timestamp)

        return combined_results, analysis

    def analyze_advanced_results(self, results_df: pd.DataFrame) -> Dict:
        """分析改進版回測結果"""
        analysis = {}

        # 總體統計
        total_trades = len(results_df)
        winning_trades = len(results_df[results_df['pnl_pct'] > 0])
        overall_win_rate = winning_trades / total_trades if total_trades > 0 else 0
        overall_avg_pnl = results_df['pnl_pct'].mean()
        overall_total_return = results_df['pnl_pct'].sum()

        # 計算夏普比率
        returns = results_df['pnl_pct'] / 100
        sharpe_ratio = returns.mean() / returns.std() if returns.std() > 0 else 0

        # 計算最大回撤
        cumulative_returns = (1 + returns).cumprod()
        rolling_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - rolling_max) / rolling_max
        max_drawdown = drawdown.min()

        analysis['overall'] = {
            'total_trades': total_trades,
            'win_rate': overall_win_rate,
            'avg_pnl': overall_avg_pnl,
            'total_return': overall_total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown * 100,
            'best_trade': results_df['pnl_pct'].max(),
            'worst_trade': results_df['pnl_pct'].min(),
            'avg_holding_periods': results_df['holding_periods'].mean()
        }

        # 按止盈止損方法分析
        analysis['by_method'] = {}
        for method in results_df['method'].unique():
            method_data = results_df[results_df['method'] == method]
            self._analyze_group(analysis['by_method'], method, method_data)

        # 按市場條件分析
        analysis['by_market_condition'] = {}
        for condition in results_df['market_condition'].unique():
            condition_data = results_df[results_df['market_condition'] == condition]
            self._analyze_group(analysis['by_market_condition'], condition, condition_data)

        # 按幣種分析
        analysis['by_symbol'] = {}
        for symbol in results_df['symbol'].unique():
            symbol_data = results_df[results_df['symbol'] == symbol]
            self._analyze_group(analysis['by_symbol'], symbol, symbol_data)

        # 按時間框架分析
        analysis['by_timeframe'] = {}
        for timeframe in results_df['timeframe'].unique():
            tf_data = results_df[results_df['timeframe'] == timeframe]
            self._analyze_group(analysis['by_timeframe'], timeframe, tf_data)

        # 按方向分析
        analysis['by_direction'] = {}
        for direction in results_df['direction'].unique():
            direction_data = results_df[results_df['direction'] == direction]
            self._analyze_group(analysis['by_direction'], direction, direction_data)

        return analysis

    def _analyze_group(self, analysis_dict: Dict, key: str, data: pd.DataFrame):
        """分析數據組"""
        if data.empty:
            return

        total = len(data)
        wins = len(data[data['pnl_pct'] > 0])
        win_rate = wins / total

        avg_win = data[data['pnl_pct'] > 0]['pnl_pct'].mean()
        avg_loss = data[data['pnl_pct'] < 0]['pnl_pct'].mean()

        tp_rate = len(data[data['exit_type'] == 'TAKE_PROFIT']) / total
        sl_rate = len(data[data['exit_type'] == 'STOP_LOSS']) / total

        returns = data['pnl_pct'] / 100
        sharpe = returns.mean() / returns.std() if returns.std() > 0 else 0

        analysis_dict[key] = {
            'total_trades': total,
            'win_rate': win_rate,
            'avg_pnl': data['pnl_pct'].mean(),
            'avg_win': avg_win if not pd.isna(avg_win) else 0,
            'avg_loss': avg_loss if not pd.isna(avg_loss) else 0,
            'total_return': data['pnl_pct'].sum(),
            'take_profit_rate': tp_rate,
            'stop_loss_rate': sl_rate,
            'avg_risk_reward': data['risk_reward_ratio'].mean(),
            'avg_confidence': data['confidence'].mean(),
            'avg_holding_periods': data['holding_periods'].mean(),
            'sharpe_ratio': sharpe,
            'max_drawdown': data['pnl_pct'].min()
        }

    def generate_advanced_report(self, results_df: pd.DataFrame, analysis: Dict, timestamp: str):
        """生成改進版回測報告"""
        print("\n" + "="*100)
        print("📈 改進版「擴充版RSI+多空力道策略」回測報告")
        print("="*100)

        overall = analysis['overall']
        print(f"\n📊 總體表現:")
        print(f"   總交易數: {overall['total_trades']}")
        print(f"   整體勝率: {overall['win_rate']:.2%}")
        print(f"   平均盈虧: {overall['avg_pnl']:.2f}%")
        print(f"   總回報: {overall['total_return']:.2f}%")
        print(f"   夏普比率: {overall['sharpe_ratio']:.3f}")
        print(f"   最大回撤: {overall['max_drawdown']:.2f}%")
        print(f"   最佳交易: {overall['best_trade']:.2f}%")
        print(f"   最差交易: {overall['worst_trade']:.2f}%")
        print(f"   平均持倉: {overall['avg_holding_periods']:.1f} 週期")

        # 止盈止損方法比較
        print(f"\n🔍 止盈止損方法表現:")
        print("-" * 100)
        self._print_analysis_table(analysis['by_method'], "方法")

        # 市場條件表現
        print(f"\n🌊 市場條件表現:")
        print("-" * 80)
        self._print_analysis_table(analysis['by_market_condition'], "市場條件")

        # 幣種表現
        print(f"\n💰 幣種表現 (前10名):")
        print("-" * 80)
        symbol_items = list(analysis['by_symbol'].items())
        symbol_items.sort(key=lambda x: x[1]['total_return'], reverse=True)
        top_symbols = dict(symbol_items[:10])
        self._print_analysis_table(top_symbols, "幣種")

        # 時間框架比較
        print(f"\n⏰ 時間框架比較:")
        print("-" * 60)
        self._print_analysis_table(analysis['by_timeframe'], "時間框架")

        # 方向比較
        print(f"\n📈📉 交易方向比較:")
        print("-" * 60)
        self._print_analysis_table(analysis['by_direction'], "方向")

        # 推薦最佳配置
        best_configs = self._get_best_configurations(analysis)
        print(f"\n🏆 最佳配置推薦:")
        print("-" * 80)
        print(f"🥇 最高勝率方法: {best_configs['best_method_winrate']['name']} ({best_configs['best_method_winrate']['win_rate']:.1%})")
        print(f"💰 最高回報方法: {best_configs['best_method_return']['name']} ({best_configs['best_method_return']['total_return']:.2f}%)")
        print(f"⚖️ 最佳夏普比率: {best_configs['best_method_sharpe']['name']} ({best_configs['best_method_sharpe']['sharpe_ratio']:.3f})")
        print(f"🎯 最佳市場條件: {best_configs['best_market_condition']['name']} ({best_configs['best_market_condition']['win_rate']:.1%} 勝率)")
        print(f"💎 最佳幣種: {best_configs['best_symbol']['name']} ({best_configs['best_symbol']['total_return']:.2f}% 回報)")
        print(f"⏱️ 最佳時間框架: {best_configs['best_timeframe']['name']} ({best_configs['best_timeframe']['win_rate']:.1%} 勝率)")

        # 保存詳細報告
        report_file = f"{self.results_dir}/advanced_report_{timestamp}.json"
        report_data = {
            'timestamp': timestamp,
            'strategy': 'Advanced RSI + Taker Intensity with Dynamic Stop Loss',
            'analysis': analysis,
            'best_configurations': best_configs,
            'test_config': {
                'symbols': self.test_symbols,
                'timeframes': self.test_timeframes,
                'rsi_thresholds': [self.rsi_short_threshold, self.rsi_long_threshold],
                'ti_lookback': self.ti_lookback,
                'confidence_level': self.confidence_level
            }
        }

        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)

        print(f"\n📄 詳細報告已保存: {report_file}")

        # 實施建議
        print(f"\n💡 實施建議:")
        print("-" * 50)
        print("1. 立即可用的最佳配置:")
        print(f"   - 止盈止損方法: {best_configs['best_method_sharpe']['name']}")
        print(f"   - 優選幣種: {best_configs['best_symbol']['name']}")
        print(f"   - 推薦時間框架: {best_configs['best_timeframe']['name']}")
        print("2. 風險控制建議:")
        print(f"   - 最大單筆風險: 2% (基於最大回撤 {overall['max_drawdown']:.1f}%)")
        print(f"   - 建議倉位: 每個信號 1% 資金")
        print("3. 優化方向:")
        print("   - 針對表現較差的市場條件調整參數")
        print("   - 考慮增加更多技術指標確認")
        print("   - 實施動態倉位管理")

    def _print_analysis_table(self, analysis_dict: Dict, key_name: str):
        """打印分析表格"""
        if not analysis_dict:
            return

        table_data = []
        for name, stats in analysis_dict.items():
            table_data.append({
                key_name: name,
                '交易數': stats['total_trades'],
                '勝率': f"{stats['win_rate']:.1%}",
                '平均盈虧': f"{stats['avg_pnl']:.2f}%",
                '總回報': f"{stats['total_return']:.2f}%",
                '夏普比率': f"{stats['sharpe_ratio']:.3f}",
                '止盈率': f"{stats['take_profit_rate']:.1%}",
                '風險回報比': f"{stats['avg_risk_reward']:.2f}"
            })

        # 按總回報排序
        table_data.sort(key=lambda x: float(x['總回報'].rstrip('%')), reverse=True)

        table_df = pd.DataFrame(table_data)
        print(table_df.to_string(index=False))

    def _get_best_configurations(self, analysis: Dict) -> Dict:
        """獲取最佳配置"""
        best_configs = {}

        # 最佳方法
        methods = analysis['by_method']
        if methods:
            best_configs['best_method_winrate'] = max(methods.items(), key=lambda x: x[1]['win_rate'])
            best_configs['best_method_return'] = max(methods.items(), key=lambda x: x[1]['total_return'])
            best_configs['best_method_sharpe'] = max(methods.items(), key=lambda x: x[1]['sharpe_ratio'])

            # 格式化
            for key in ['best_method_winrate', 'best_method_return', 'best_method_sharpe']:
                name, stats = best_configs[key]
                best_configs[key] = {'name': name, **stats}

        # 最佳市場條件
        conditions = analysis['by_market_condition']
        if conditions:
            name, stats = max(conditions.items(), key=lambda x: x[1]['win_rate'])
            best_configs['best_market_condition'] = {'name': name, **stats}

        # 最佳幣種
        symbols = analysis['by_symbol']
        if symbols:
            name, stats = max(symbols.items(), key=lambda x: x[1]['total_return'])
            best_configs['best_symbol'] = {'name': name, **stats}

        # 最佳時間框架
        timeframes = analysis['by_timeframe']
        if timeframes:
            name, stats = max(timeframes.items(), key=lambda x: x[1]['win_rate'])
            best_configs['best_timeframe'] = {'name': name, **stats}

        return best_configs

async def main():
    """主函數"""
    print("🎯 改進版「擴充版RSI+多空力道策略」回測系統")
    print("=" * 80)
    print("📊 特色功能:")
    print("   ✅ 15個主流幣種全覆蓋")
    print("   ✅ 1H/4H雙時間框架")
    print("   ✅ RSI+Taker Intensity (5選4邏輯)")
    print("   ✅ 7種動態止盈止損方法")
    print("   ✅ 市場條件自適應")
    print("   ✅ 全面性能分析")
    print("=" * 80)

    async with AdvancedRSITIBacktester() as backtester:
        try:
            results, analysis = await backtester.run_comprehensive_backtest()

            if results is not None and analysis is not None:
                print(f"\n✅ 回測完成！")
                print(f"📊 總共測試了 {len(results)} 筆交易")
                print(f"📁 結果文件保存在 {backtester.results_dir}/ 目錄中")

                # 快速摘要
                overall = analysis['overall']
                print(f"\n🎉 快速摘要:")
                print(f"   勝率: {overall['win_rate']:.1%}")
                print(f"   總回報: {overall['total_return']:.2f}%")
                print(f"   夏普比率: {overall['sharpe_ratio']:.3f}")

            else:
                print("\n❌ 回測失敗，無法獲取足夠的數據")

        except Exception as e:
            logger.error(f"❌ 回測過程中出現錯誤: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
