#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OBV+TI+BB策略實盤交易系統監控腳本
監控系統運行狀態、交易表現和風險指標
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os
import logging
import requests
import time
from typing import Dict, List, Optional

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TradingSystemMonitor:
    """交易系統監控器"""
    
    def __init__(self):
        self.telegram_bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
        self.telegram_chat_id = os.getenv('TELEGRAM_CHAT_ID')
        
    def send_telegram_message(self, message: str):
        """發送Telegram消息"""
        try:
            if not self.telegram_bot_token or not self.telegram_chat_id:
                logger.warning("Telegram配置未設置，跳過消息發送")
                return
                
            url = f"https://api.telegram.org/bot{self.telegram_bot_token}/sendMessage"
            payload = {
                'chat_id': self.telegram_chat_id,
                'text': message,
                'parse_mode': 'HTML'
            }
            response = requests.post(url, json=payload, timeout=10)
            if response.status_code == 200:
                logger.info("✅ 監控消息發送成功")
            else:
                logger.error(f"❌ 監控消息發送失敗: {response.status_code}")
        except Exception as e:
            logger.error(f"❌ 發送監控消息異常: {e}")
    
    def check_system_health(self) -> Dict:
        """檢查系統健康狀態"""
        health_status = {
            'timestamp': datetime.now(),
            'system_running': False,
            'container_status': 'unknown',
            'log_file_exists': False,
            'recent_activity': False,
            'disk_space_ok': True,
            'memory_usage_ok': True
        }
        
        try:
            # 檢查Docker容器狀態
            import subprocess
            result = subprocess.run(['docker', 'ps', '--filter', 'name=obv_ti_bb_trading', '--format', 'table {{.Status}}'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0 and 'Up' in result.stdout:
                health_status['system_running'] = True
                health_status['container_status'] = 'running'
            else:
                health_status['container_status'] = 'stopped'
            
            # 檢查日誌文件
            log_file = 'obv_live_trading.log'
            if os.path.exists(log_file):
                health_status['log_file_exists'] = True
                
                # 檢查最近活動
                file_mod_time = datetime.fromtimestamp(os.path.getmtime(log_file))
                if datetime.now() - file_mod_time < timedelta(minutes=5):
                    health_status['recent_activity'] = True
            
            # 檢查磁盤空間
            import shutil
            disk_usage = shutil.disk_usage('.')
            free_space_gb = disk_usage.free / (1024**3)
            if free_space_gb < 1:  # 少於1GB
                health_status['disk_space_ok'] = False
            
        except Exception as e:
            logger.error(f"健康檢查異常: {e}")
        
        return health_status
    
    def analyze_trading_performance(self) -> Dict:
        """分析交易表現"""
        performance = {
            'timestamp': datetime.now(),
            'total_trades_today': 0,
            'active_trades': 0,
            'win_rate_today': 0,
            'total_pnl_today': 0,
            'avg_signal_strength': 0,
            'top_performing_symbols': [],
            'risk_alerts': []
        }
        
        try:
            # 讀取今日交易記錄
            today = datetime.now().strftime('%Y%m%d')
            trade_file = f'obv_live_trades_{today}.csv'
            
            if os.path.exists(trade_file):
                df = pd.read_csv(trade_file)
                performance['total_trades_today'] = len(df)
                
                if len(df) > 0:
                    performance['avg_signal_strength'] = df['signal_strength'].mean()
                    
                    # 統計各幣種表現
                    symbol_stats = df.groupby('symbol').agg({
                        'signal_strength': 'mean',
                        'direction': 'count'
                    }).round(3)
                    
                    performance['top_performing_symbols'] = symbol_stats.head(5).to_dict()
            
            # 檢查風險指標
            if performance['total_trades_today'] > 20:
                performance['risk_alerts'].append('今日交易次數過多')
            
            if performance['avg_signal_strength'] < 0.7:
                performance['risk_alerts'].append('平均信號強度偏低')
            
        except Exception as e:
            logger.error(f"交易表現分析異常: {e}")
        
        return performance
    
    def generate_monitoring_report(self) -> str:
        """生成監控報告"""
        health = self.check_system_health()
        performance = self.analyze_trading_performance()
        
        # 系統狀態emoji
        system_emoji = "🟢" if health['system_running'] else "🔴"
        activity_emoji = "✅" if health['recent_activity'] else "⚠️"
        
        report = f"""
🤖 <b>OBV+TI+BB系統監控報告</b>

{system_emoji} <b>系統狀態:</b> {'運行中' if health['system_running'] else '已停止'}
📊 <b>容器狀態:</b> {health['container_status']}
{activity_emoji} <b>最近活動:</b> {'正常' if health['recent_activity'] else '無活動'}

📈 <b>交易表現:</b>
• 今日信號: {performance['total_trades_today']}筆
• 活躍交易: {performance['active_trades']}筆
• 平均信號強度: {performance['avg_signal_strength']:.1%}

💾 <b>系統資源:</b>
• 磁盤空間: {'正常' if health['disk_space_ok'] else '不足'}
• 內存使用: {'正常' if health['memory_usage_ok'] else '過高'}
• 日誌文件: {'存在' if health['log_file_exists'] else '缺失'}

⚠️ <b>風險提醒:</b>
{chr(10).join(['• ' + alert for alert in performance['risk_alerts']]) if performance['risk_alerts'] else '• 無風險提醒'}

⏰ {health['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        return report.strip()
    
    async def run_monitoring_loop(self):
        """運行監控循環"""
        logger.info("🔍 啟動OBV+TI+BB系統監控")
        
        while True:
            try:
                # 生成監控報告
                report = self.generate_monitoring_report()
                
                # 發送報告 (每小時發送一次)
                current_time = datetime.now()
                if current_time.minute == 0:  # 整點發送
                    self.send_telegram_message(report)
                    logger.info("📊 監控報告已發送")
                
                # 檢查系統健康狀態
                health = self.check_system_health()
                
                # 如果系統停止運行，立即發送警報
                if not health['system_running']:
                    alert_message = f"""
🚨 <b>系統警報</b>

❌ OBV+TI+BB交易系統已停止運行
📊 容器狀態: {health['container_status']}
⏰ 檢測時間: {health['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}

🔧 請檢查系統狀態並重啟服務
                    """
                    self.send_telegram_message(alert_message.strip())
                    logger.error("🚨 系統停止運行警報已發送")
                
                # 等待5分鐘後再次檢查
                await asyncio.sleep(300)
                
            except KeyboardInterrupt:
                logger.info("🛑 監控系統停止")
                break
            except Exception as e:
                logger.error(f"❌ 監控循環異常: {e}")
                await asyncio.sleep(60)  # 異常後等待1分鐘

def main():
    """主函數"""
    try:
        # 加載環境變量
        from dotenv import load_dotenv
        load_dotenv()
        
        # 創建監控器
        monitor = TradingSystemMonitor()
        
        # 運行監控循環
        asyncio.run(monitor.run_monitoring_loop())
        
    except Exception as e:
        logger.error(f"❌ 監控系統啟動失敗: {e}")

if __name__ == "__main__":
    main()
