"""
日誌設置模塊
提供統一的日誌配置和管理功能
"""

import logging
import os
from datetime import datetime
from logging.handlers import RotatingFileHandler


def setup_logger(name: str = "signal_system", level: str = "INFO") -> logging.Logger:
    """
    設置並返回配置好的日誌記錄器
    
    Args:
        name: 日誌記錄器名稱
        level: 日誌級別 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    
    Returns:
        配置好的日誌記錄器
    """
    # 創建日誌目錄
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 創建日誌記錄器
    logger = logging.getLogger(name)
    
    # 如果已經配置過，直接返回
    if logger.handlers:
        return logger
    
    # 設置日誌級別
    log_level = getattr(logging, level.upper(), logging.INFO)
    logger.setLevel(log_level)
    
    # 創建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台處理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件處理器（輪轉日誌）
    log_file = os.path.join(log_dir, f"{name}.log")
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setLevel(log_level)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    return logger


def get_logger(name: str = "signal_system") -> logging.Logger:
    """
    獲取已配置的日誌記錄器
    
    Args:
        name: 日誌記錄器名稱
    
    Returns:
        日誌記錄器
    """
    return logging.getLogger(name)
