#!/usr/bin/env python3
"""
測試新的CSV交易記錄系統
"""

import sys
import os
from datetime import datetime

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from main import MainSignalSystem

def test_csv_system():
    """測試CSV系統功能"""
    print("🧪 測試CSV交易記錄系統")
    
    # 創建系統實例
    system = MainSignalSystem()
    
    # 測試1: 檢查CSV文件創建
    print("\n1️⃣ 測試CSV文件創建...")
    if os.path.exists(system.trades_csv_path):
        print("✅ CSV文件已創建")
    else:
        print("❌ CSV文件創建失敗")
        return
    
    # 測試2: 保存測試交易
    print("\n2️⃣ 測試保存交易記錄...")
    test_trade = {
        'trade_id': 'TEST_BTCUSDT_1H_20250714_220000',
        'symbol': 'BTCUSDT',
        'timeframe': '1H',
        'direction': 'LONG',
        'entry_price': 121870.0,
        'stop_loss_price': 120747.0,
        'take_profit_price': 123553.0,
        'entry_time': datetime.now().isoformat(),
        'exit_time': '',
        'exit_price': '',
        'exit_type': '',
        'pnl_pct': '',
        'status': 'ACTIVE'
    }
    
    system.save_trade_to_csv(test_trade)
    print("✅ 測試交易已保存")
    
    # 測試3: 讀取活躍交易
    print("\n3️⃣ 測試讀取活躍交易...")
    active_trades = system.get_active_trades_from_csv()
    print(f"✅ 找到 {len(active_trades)} 個活躍交易")
    for trade in active_trades:
        print(f"   - {trade['symbol']} {trade['timeframe']} {trade['direction']}")
    
    # 測試4: 更新交易記錄
    print("\n4️⃣ 測試更新交易記錄...")
    update_data = {
        'exit_time': datetime.now().isoformat(),
        'exit_price': 122500.0,
        'exit_type': 'TAKE_PROFIT',
        'pnl_pct': 0.52,
        'status': 'CLOSED'
    }
    system.update_trade_in_csv(test_trade['trade_id'], update_data)
    print("✅ 交易記錄已更新")
    
    # 測試5: 獲取統計數據
    print("\n5️⃣ 測試統計數據...")
    stats = system.get_trading_statistics()
    print(f"✅ 統計數據:")
    print(f"   - 活躍交易: {stats['active_count']}個")
    print(f"   - 今日完成: {stats['today_closed']}個")
    print(f"   - 今日盈虧: {stats['today_pnl']:+.2f}%")
    print(f"   - 今日勝率: {stats['today_win_rate']:.1f}%")
    print(f"   - 總交易: {stats['total_trades']}個")
    print(f"   - 總盈虧: {stats['total_pnl']:+.2f}%")
    print(f"   - 總勝率: {stats['total_win_rate']:.1f}%")
    
    # 測試6: 檢查重複信號
    print("\n6️⃣ 測試重複信號檢查...")
    signal_key = "BTCUSDT_1H_20250714_22"
    is_duplicate = system.is_duplicate_signal(signal_key)
    print(f"✅ 重複信號檢查: {is_duplicate}")
    
    print("\n🎉 CSV系統測試完成！")

if __name__ == "__main__":
    test_csv_system()
