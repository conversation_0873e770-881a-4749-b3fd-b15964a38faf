#!/usr/bin/env python3
"""
API連接測試腳本
測試Blave API和Bybit API的連接狀態
"""

import asyncio
import aiohttp
import os
import sys
from datetime import datetime

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config_manager import ConfigManager
from src.data_fetcher import DataFetcher

async def test_api_connections():
    """測試API連接"""
    print("🔍 開始測試API連接...")
    
    # 初始化配置
    config = ConfigManager()
    
    # 測試符號
    test_symbol = "BTCUSDT"
    test_timeframe = "1H"
    
    async with DataFetcher(config) as fetcher:
        print(f"\n📊 測試 {test_symbol} {test_timeframe} 數據獲取...")
        
        # 測試Bybit API
        print("\n1️⃣ 測試Bybit API...")
        bybit_data = await fetcher.get_bybit_data(test_symbol, test_timeframe)
        if bybit_data is not None:
            print(f"✅ Bybit API成功: {len(bybit_data)}條記錄")
        else:
            print("❌ Bybit API失敗")
        
        # 測試Blave籌碼集中度API
        print("\n2️⃣ 測試Blave籌碼集中度API...")
        concentration_data = await fetcher.get_blave_concentration_data(test_symbol, test_timeframe)
        if concentration_data is not None:
            print(f"✅ Blave籌碼集中度API成功: {len(concentration_data)}條記錄")
        else:
            print("❌ Blave籌碼集中度API失敗")
        
        # 測試Blave Taker Intensity API
        print("\n3️⃣ 測試Blave Taker Intensity API...")
        intensity_data = await fetcher.get_blave_taker_intensity_data(test_symbol, test_timeframe)
        if intensity_data is not None:
            print(f"✅ Blave Taker Intensity API成功: {len(intensity_data)}條記錄")
        else:
            print("❌ Blave Taker Intensity API失敗")
        
        # 測試完整數據獲取
        print("\n4️⃣ 測試完整數據獲取...")
        complete_data = await fetcher.get_latest_data(test_symbol, test_timeframe)
        if complete_data is not None:
            print(f"✅ 完整數據獲取成功: {len(complete_data)}條記錄")
            print(f"📋 數據列: {list(complete_data.columns)}")
        else:
            print("❌ 完整數據獲取失敗")

async def test_telegram_connection():
    """測試Telegram連接"""
    print("\n📱 測試Telegram連接...")
    
    bot_token = os.getenv('TELEGRAM_BOT_TOKEN', 'YOUR_BOT_TOKEN')
    chat_id = os.getenv('TELEGRAM_CHAT_ID', 'YOUR_CHAT_ID')
    
    if bot_token == 'YOUR_BOT_TOKEN' or chat_id == 'YOUR_CHAT_ID':
        print("❌ Telegram配置未設置")
        return
    
    try:
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        payload = {
            'chat_id': chat_id,
            'text': '🧪 API連接測試消息',
            'parse_mode': 'HTML'
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload) as response:
                if response.status == 200:
                    print("✅ Telegram連接成功")
                else:
                    print(f"❌ Telegram連接失敗: {response.status}")
                    
    except Exception as e:
        print(f"❌ Telegram測試錯誤: {e}")

async def main():
    """主函數"""
    print("🚀 API連接測試開始")
    print(f"⏰ 測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    await test_api_connections()
    await test_telegram_connection()
    
    print("\n✅ API連接測試完成")

if __name__ == "__main__":
    asyncio.run(main())
