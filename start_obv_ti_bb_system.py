#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OBV+TI+BB策略實盤交易系統啟動腳本
"""

import asyncio
import sys
import os
import logging
from datetime import datetime

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(__file__))

from obv_ti_bb_production_system import OBVTIBBLiveTradingSystem

def setup_logging():
    """設置日誌"""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 創建logs目錄
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # 設置日誌文件名
    log_filename = f"logs/obv_ti_bb_{datetime.now().strftime('%Y%m%d')}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def print_banner():
    """打印系統橫幅"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                        OBV+TI+BB實盤交易系統                                  ║
║                                                                              ║
║  📊 策略特點: OBV成交量 + Taker Intensity + 布林帶中軌突破                    ║
║  📈 回測績效: 78.7%勝率, 1.98夏普比率                                        ║
║  🎯 信號邏輯: 4選3條件確認，SUPERTREND動態止盈止損                           ║
║  ⚡ 掃描頻率: 每分鐘掃描，實時監控市場機會                                    ║
║                                                                              ║
║  ⚠️  風險提示: 請確保充分理解策略邏輯和風險控制機制                           ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_prerequisites():
    """檢查運行前提條件"""
    logger = logging.getLogger(__name__)
    
    # 檢查配置文件
    required_files = [
        'obv_ti_bb_config.json',
        'rsi_signal_config.json',
        'src/config_manager.py',
        'src/data_fetcher.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        logger.error(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    # 檢查src目錄
    if not os.path.exists('src'):
        logger.error("❌ 缺少src目錄，請確保項目結構完整")
        return False
    
    logger.info("✅ 前提條件檢查通過")
    return True

async def main():
    """主函數"""
    # 設置日誌
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # 打印橫幅
    print_banner()
    
    # 檢查前提條件
    if not check_prerequisites():
        logger.error("❌ 前提條件檢查失敗，系統退出")
        return
    
    try:
        logger.info("🚀 正在啟動OBV+TI+BB實盤交易系統...")
        
        # 創建交易系統實例
        trading_system = OBVTIBBLiveTradingSystem()
        
        # 檢查策略配置
        if not trading_system.strategies:
            logger.error("❌ 未找到有效的策略配置")
            return
        
        logger.info(f"✅ 成功加載 {len(trading_system.strategies)} 個交易策略")
        
        # 顯示策略列表
        logger.info("📋 活躍策略列表:")
        for strategy_key, config in trading_system.strategies.items():
            logger.info(f"   - {strategy_key}: {config['symbol']} {config['timeframe']}")
        
        # 啟動交易循環
        logger.info("🔄 開始交易循環...")
        await trading_system.run_trading_loop()
        
    except KeyboardInterrupt:
        logger.info("👋 用戶手動停止系統")
    except Exception as e:
        logger.error(f"❌ 系統運行異常: {e}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        logger.info("🛑 OBV+TI+BB實盤交易系統已停止")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 系統已停止")
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")
        import traceback
        traceback.print_exc()
