#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整歷史回測系統 - 平衡版領先策略
對所有19個活躍策略進行2年歷史數據回測分析
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os
import sys
import logging
import traceback
from typing import Dict, List, Optional, Tuple

# 添加src目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.data_fetcher import DataFetcher
from src.config_manager import ConfigManager

# 設置日誌
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('backtest_system.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TechnicalIndicators:
    """技術指標計算類"""
    
    def bollinger_bands(self, prices, window=20, std_dev=2):
        """計算布林帶"""
        sma = prices.rolling(window=window).mean()
        std = prices.rolling(window=window).std()
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        return upper, sma, lower
    
    def rsi(self, prices, period=14):
        """計算RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def atr(self, high, low, close, period=14):
        """計算ATR"""
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(window=period).mean()
        return atr
    
    def calculate_ti_confidence_interval(self, ti_values, lookback=24, confidence=0.7):
        """計算TI信賴區間"""
        rolling_ti = ti_values.rolling(window=lookback)
        upper_percentile = (1 + confidence) / 2
        lower_percentile = (1 - confidence) / 2
        
        upper_limit = rolling_ti.quantile(upper_percentile)
        lower_limit = rolling_ti.quantile(lower_percentile)
        
        return upper_limit, lower_limit

class HistoricalDataManager:
    """歷史數據管理器"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.data_fetcher = DataFetcher(config_manager)
        
    async def get_historical_data_range(self, symbol: str, timeframe: str, 
                                      start_date: datetime, end_date: datetime) -> Optional[pd.DataFrame]:
        """獲取指定時間範圍的歷史數據"""
        try:
            logger.info(f"📊 獲取 {symbol} {timeframe} 歷史數據: {start_date.date()} 到 {end_date.date()}")
            
            # 計算需要的數據點數量
            if timeframe == '1h':
                total_hours = int((end_date - start_date).total_seconds() / 3600)
                required_points = min(total_hours, 2000)  # 限制最大數據點
            elif timeframe == '4h':
                total_4h_periods = int((end_date - start_date).total_seconds() / (4 * 3600))
                required_points = min(total_4h_periods, 1500)
            else:
                required_points = 730  # 默認2年日數據
            
            # 分批獲取數據以避免API限制
            all_data = []
            current_end = end_date
            batch_size = 200  # 每批200個數據點
            
            while len(all_data) < required_points and current_end > start_date:
                # 計算當前批次的開始時間
                if timeframe == '1h':
                    batch_start = current_end - timedelta(hours=batch_size)
                elif timeframe == '4h':
                    batch_start = current_end - timedelta(hours=batch_size * 4)
                else:
                    batch_start = current_end - timedelta(days=batch_size)
                
                batch_start = max(batch_start, start_date)
                
                # 獲取當前批次數據
                batch_data = await self.data_fetcher.get_latest_data(symbol, timeframe)
                
                if batch_data is not None and len(batch_data) > 0:
                    # 過濾時間範圍
                    batch_data = batch_data[
                        (batch_data.index >= batch_start) & 
                        (batch_data.index <= current_end)
                    ]
                    
                    if len(batch_data) > 0:
                        all_data.append(batch_data)
                        logger.info(f"   獲取批次數據: {len(batch_data)} 條 ({batch_start.date()} 到 {current_end.date()})")
                
                current_end = batch_start - timedelta(minutes=1)
                
                # 避免過度請求
                await asyncio.sleep(0.1)
            
            if not all_data:
                logger.warning(f"❌ {symbol} {timeframe} 無法獲取歷史數據")
                return None
            
            # 合併所有批次數據
            combined_data = pd.concat(all_data, axis=0)
            combined_data = combined_data.sort_index().drop_duplicates()
            
            # 確保數據在指定時間範圍內
            combined_data = combined_data[
                (combined_data.index >= start_date) & 
                (combined_data.index <= end_date)
            ]
            
            logger.info(f"✅ {symbol} {timeframe} 歷史數據獲取完成: {len(combined_data)} 條記錄")
            return combined_data
            
        except Exception as e:
            logger.error(f"❌ {symbol} {timeframe} 歷史數據獲取失敗: {e}")
            logger.error(traceback.format_exc())
            return None

class BalancedLeadingBacktester:
    """平衡版領先策略回測器"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.data_manager = HistoricalDataManager(self.config_manager)
        self.indicators = TechnicalIndicators()
        
        # 加載策略配置
        self.strategies = self.load_strategies()
        
        # 回測結果存儲
        self.backtest_results = {}
        self.trade_records = []
        
    def load_strategies(self) -> Dict:
        """加載所有策略配置"""
        try:
            with open('rsi_signal_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('active_strategies', {})
        except Exception as e:
            logger.error(f"加載策略配置失敗: {e}")
            return {}
    
    def calculate_balanced_signals(self, data: pd.DataFrame, strategy_config: Dict) -> Optional[Dict]:
        """
        計算平衡版領先策略信號
        核心邏輯：BB中軌突破 + TI動能確認 + RSI過濾 + 價格動能確認
        """
        try:
            if len(data) < 50:  # 確保有足夠數據計算指標
                return None
                
            # 獲取策略參數
            bb_window = strategy_config.get('bb_window', 20)
            bb_std = strategy_config.get('bb_std', 2.0)
            rsi_period = strategy_config.get('rsi_period', 14)
            atr_period = strategy_config.get('atr_period', 14)
            ti_lookback = strategy_config.get('ti_lookback', 24)
            ti_confidence = strategy_config.get('ti_confidence', 0.7)
            
            # 計算技術指標
            bb_upper, bb_middle, bb_lower = self.indicators.bollinger_bands(
                data['Close'], window=bb_window, std_dev=bb_std
            )
            rsi = self.indicators.rsi(data['Close'], period=rsi_period)
            atr = self.indicators.atr(data['High'], data['Low'], data['Close'], period=atr_period)
            
            # 計算TI信賴區間 (使用淨多空力道)
            ti_data = data['long_taker_intensity'] - data['short_taker_intensity']
            ti_upper_limit, ti_lower_limit = self.indicators.calculate_ti_confidence_interval(
                ti_data, lookback=ti_lookback, confidence=ti_confidence
            )
            
            # 獲取最新值
            current_price = data['Close'].iloc[-1]
            prev_price = data['Close'].iloc[-2] if len(data) > 1 else current_price
            prev2_price = data['Close'].iloc[-3] if len(data) > 2 else prev_price
            
            current_rsi = rsi.iloc[-1]
            current_ti = ti_data.iloc[-1]
            prev_ti = ti_data.iloc[-2] if len(ti_data) > 1 else current_ti
            
            current_bb_middle = bb_middle.iloc[-1]
            prev_bb_middle = bb_middle.iloc[-2] if len(bb_middle) > 1 else current_bb_middle
            
            current_ti_upper = ti_upper_limit.iloc[-1]
            current_ti_lower = ti_lower_limit.iloc[-1]
            
            # 計算價格動能
            price_momentum = (current_price - prev2_price) / prev2_price * 100 if prev2_price != 0 else 0
            
            # 平衡版多頭信號條件 (5選4)
            long_conditions = {
                'price_near_bb_middle_up': current_price > current_bb_middle and prev_price <= prev_bb_middle,
                'ti_positive_momentum': current_ti > 0 and current_ti > prev_ti,
                'rsi_not_overbought': current_rsi < 75,
                'price_momentum_up': price_momentum > 0.5,
                'ti_extreme_high': current_ti > current_ti_upper
            }
            
            # 平衡版空頭信號條件 (5選4)
            short_conditions = {
                'price_near_bb_middle_down': current_price < current_bb_middle and prev_price >= prev_bb_middle,
                'ti_negative_momentum': current_ti < 0 and current_ti < prev_ti,
                'rsi_not_oversold': current_rsi > 25,
                'price_momentum_down': price_momentum < -0.5,
                'ti_extreme_low': current_ti < current_ti_lower
            }
            
            # 計算滿足條件數量
            long_count = sum(long_conditions.values())
            short_count = sum(short_conditions.values())
            
            # 生成信號 (需要5個條件中滿足4個)
            signal = None
            signal_strength = 0
            
            if long_count >= 4:
                signal = 'LONG'
                signal_strength = long_count / 5
            elif short_count >= 4:
                signal = 'SHORT'
                signal_strength = short_count / 5
            
            return {
                'signal': signal,
                'signal_strength': signal_strength,
                'current_price': current_price,
                'current_rsi': current_rsi,
                'current_ti': current_ti,
                'bb_upper': bb_upper.iloc[-1],
                'bb_middle': current_bb_middle,
                'bb_lower': bb_lower.iloc[-1],
                'price_momentum': price_momentum,
                'long_conditions': long_conditions,
                'short_conditions': short_conditions,
                'long_count': long_count,
                'short_count': short_count,
                'atr': atr.iloc[-1] if not pd.isna(atr.iloc[-1]) else 0
            }
            
        except Exception as e:
            logger.error(f"計算平衡版信號失敗: {e}")
            return None
    
    def calculate_supertrend_stop_loss(self, data: pd.DataFrame, direction: str, 
                                     entry_price: float, period: int = 10, 
                                     multiplier: float = 3.0) -> Optional[Dict]:
        """計算SUPERTREND止盈止損"""
        try:
            if len(data) < period:
                return None
                
            # 計算ATR和中位價
            hl2 = (data['High'] + data['Low']) / 2
            atr = self.indicators.atr(data['High'], data['Low'], data['Close'], period=period)
            
            # 計算上下軌
            upper_band = hl2 + (multiplier * atr)
            lower_band = hl2 - (multiplier * atr)
            
            # SUPERTREND邏輯
            supertrend = np.where(data['Close'] <= lower_band.shift(1), lower_band, 
                                 np.where(data['Close'] >= upper_band.shift(1), upper_band, np.nan))
            
            # 前向填充
            supertrend = pd.Series(supertrend, index=data.index).fillna(method='ffill')
            
            if len(supertrend) == 0 or pd.isna(supertrend.iloc[-1]):
                return None
                
            current_supertrend = supertrend.iloc[-1]
            
            # 計算止盈止損
            if direction == 'LONG':
                stop_loss = current_supertrend
                take_profit = entry_price + (abs(entry_price - current_supertrend) * 2.5)
            else:  # SHORT
                stop_loss = current_supertrend
                take_profit = entry_price - (abs(entry_price - current_supertrend) * 2.5)
            
            return {
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'risk_reward_ratio': 2.5,
                'method': 'SUPERTREND',
                'confidence': 0.78
            }
            
        except Exception as e:
            logger.error(f"SUPERTREND計算失敗: {e}")
            return None

    async def backtest_single_strategy(self, strategy_key: str, strategy_config: Dict) -> Optional[Dict]:
        """回測單個策略"""
        symbol = strategy_config['symbol']
        timeframe = strategy_config['timeframe']

        logger.info(f"🔍 開始回測策略: {strategy_key} ({symbol} {timeframe})")

        try:
            # 設置回測時間範圍 (2年)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=730)

            # 獲取歷史數據
            data = await self.data_manager.get_historical_data_range(
                symbol, timeframe, start_date, end_date
            )

            if data is None or len(data) < 200:
                logger.warning(f"❌ {strategy_key} 數據不足，跳過回測")
                return None

            logger.info(f"📊 {strategy_key} 開始回測，數據量: {len(data)} 條")

            # 執行回測
            trades = []

            # 從第200個數據點開始回測，確保指標計算穩定
            for i in range(200, len(data)):
                current_data = data.iloc[:i+1]

                # 計算信號
                signal_info = self.calculate_balanced_signals(current_data, strategy_config)

                if signal_info and signal_info['signal']:
                    # 計算止盈止損
                    stop_info = self.calculate_supertrend_stop_loss(
                        current_data, signal_info['signal'], signal_info['current_price']
                    )

                    if stop_info:
                        trade = {
                            'strategy': strategy_key,
                            'timestamp': current_data.index[-1],
                            'symbol': symbol,
                            'timeframe': timeframe,
                            'direction': signal_info['signal'],
                            'entry_price': signal_info['current_price'],
                            'stop_loss': stop_info['stop_loss'],
                            'take_profit': stop_info['take_profit'],
                            'signal_strength': signal_info['signal_strength'],
                            'rsi': signal_info['current_rsi'],
                            'ti': signal_info['current_ti'],
                            'price_momentum': signal_info['price_momentum'],
                            'long_count': signal_info['long_count'],
                            'short_count': signal_info['short_count'],
                            'atr': signal_info['atr']
                        }

                        # 模擬交易結果
                        exit_result = self.simulate_trade_exit(data, i, trade)
                        if exit_result:
                            trade.update(exit_result)
                            trades.append(trade)

                            # 記錄交易
                            self.trade_records.append(trade.copy())

            # 計算策略績效
            if trades:
                performance = self.calculate_performance_metrics(trades, strategy_key)
                logger.info(f"✅ {strategy_key} 回測完成: {len(trades)}筆交易, 總回報: {performance['total_return']:.2f}%")
                return performance
            else:
                logger.warning(f"⚠️ {strategy_key} 無交易信號")
                return {
                    'strategy': strategy_key,
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'total_trades': 0,
                    'total_return': 0,
                    'win_rate': 0,
                    'max_drawdown': 0,
                    'sharpe_ratio': 0,
                    'profit_factor': 0,
                    'signal_frequency': 0
                }

        except Exception as e:
            logger.error(f"❌ {strategy_key} 回測失敗: {e}")
            logger.error(traceback.format_exc())
            return None

    def simulate_trade_exit(self, data: pd.DataFrame, entry_index: int, trade: Dict) -> Optional[Dict]:
        """模擬交易平倉"""
        try:
            entry_price = trade['entry_price']
            stop_loss = trade['stop_loss']
            take_profit = trade['take_profit']
            direction = trade['direction']

            # 從入場後開始檢查
            for i in range(entry_index + 1, len(data)):
                current_high = data['High'].iloc[i]
                current_low = data['Low'].iloc[i]
                current_close = data['Close'].iloc[i]

                if direction == 'LONG':
                    # 檢查止損
                    if current_low <= stop_loss:
                        pnl_pct = (stop_loss - entry_price) / entry_price * 100
                        return {
                            'exit_price': stop_loss,
                            'exit_time': data.index[i],
                            'exit_type': 'STOP_LOSS',
                            'pnl_pct': pnl_pct,
                            'duration_hours': (data.index[i] - trade['timestamp']).total_seconds() / 3600
                        }
                    # 檢查止盈
                    elif current_high >= take_profit:
                        pnl_pct = (take_profit - entry_price) / entry_price * 100
                        return {
                            'exit_price': take_profit,
                            'exit_time': data.index[i],
                            'exit_type': 'TAKE_PROFIT',
                            'pnl_pct': pnl_pct,
                            'duration_hours': (data.index[i] - trade['timestamp']).total_seconds() / 3600
                        }

                else:  # SHORT
                    # 檢查止損
                    if current_high >= stop_loss:
                        pnl_pct = (entry_price - stop_loss) / entry_price * 100
                        return {
                            'exit_price': stop_loss,
                            'exit_time': data.index[i],
                            'exit_type': 'STOP_LOSS',
                            'pnl_pct': pnl_pct,
                            'duration_hours': (data.index[i] - trade['timestamp']).total_seconds() / 3600
                        }
                    # 檢查止盈
                    elif current_low <= take_profit:
                        pnl_pct = (entry_price - take_profit) / entry_price * 100
                        return {
                            'exit_price': take_profit,
                            'exit_time': data.index[i],
                            'exit_type': 'TAKE_PROFIT',
                            'pnl_pct': pnl_pct,
                            'duration_hours': (data.index[i] - trade['timestamp']).total_seconds() / 3600
                        }

            # 如果沒有觸發止盈止損，用最後價格平倉
            final_price = data['Close'].iloc[-1]
            if direction == 'LONG':
                pnl_pct = (final_price - entry_price) / entry_price * 100
            else:
                pnl_pct = (entry_price - final_price) / entry_price * 100

            return {
                'exit_price': final_price,
                'exit_time': data.index[-1],
                'exit_type': 'MARKET_CLOSE',
                'pnl_pct': pnl_pct,
                'duration_hours': (data.index[-1] - trade['timestamp']).total_seconds() / 3600
            }

        except Exception as e:
            logger.error(f"模擬交易平倉失敗: {e}")
            return None

    def calculate_performance_metrics(self, trades: List[Dict], strategy_key: str) -> Dict:
        """計算策略績效指標"""
        if not trades:
            return {}

        df = pd.DataFrame(trades)

        # 基本統計
        total_trades = len(df)
        winning_trades = len(df[df['pnl_pct'] > 0])
        losing_trades = len(df[df['pnl_pct'] <= 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        # 盈虧統計
        total_pnl = df['pnl_pct'].sum()
        avg_win = df[df['pnl_pct'] > 0]['pnl_pct'].mean() if winning_trades > 0 else 0
        avg_loss = df[df['pnl_pct'] <= 0]['pnl_pct'].mean() if losing_trades > 0 else 0

        # 最大回撤
        cumulative_pnl = df['pnl_pct'].cumsum()
        running_max = cumulative_pnl.expanding().max()
        drawdown = cumulative_pnl - running_max
        max_drawdown = drawdown.min()

        # 盈利因子
        gross_profit = df[df['pnl_pct'] > 0]['pnl_pct'].sum()
        gross_loss = abs(df[df['pnl_pct'] <= 0]['pnl_pct'].sum())
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')

        # 夏普比率 (簡化版)
        returns_std = df['pnl_pct'].std()
        sharpe_ratio = (df['pnl_pct'].mean() / returns_std) if returns_std > 0 else 0

        # 信號頻率 (每天)
        if len(df) > 0:
            time_span = (df['timestamp'].max() - df['timestamp'].min()).days
            signal_frequency = total_trades / max(time_span, 1)
        else:
            signal_frequency = 0

        # 平均持倉時間
        avg_duration = df['duration_hours'].mean() if 'duration_hours' in df.columns else 0

        # 最大連續虧損
        consecutive_losses = 0
        max_consecutive_losses = 0
        for pnl in df['pnl_pct']:
            if pnl <= 0:
                consecutive_losses += 1
                max_consecutive_losses = max(max_consecutive_losses, consecutive_losses)
            else:
                consecutive_losses = 0

        # 止盈止損統計
        tp_count = len(df[df['exit_type'] == 'TAKE_PROFIT'])
        sl_count = len(df[df['exit_type'] == 'STOP_LOSS'])
        tp_rate = tp_count / total_trades if total_trades > 0 else 0
        sl_rate = sl_count / total_trades if total_trades > 0 else 0

        return {
            'strategy': strategy_key,
            'symbol': df['symbol'].iloc[0],
            'timeframe': df['timeframe'].iloc[0],
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'total_return': total_pnl,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'max_drawdown': max_drawdown,
            'profit_factor': profit_factor,
            'sharpe_ratio': sharpe_ratio,
            'signal_frequency': signal_frequency,
            'avg_duration_hours': avg_duration,
            'max_consecutive_losses': max_consecutive_losses,
            'take_profit_rate': tp_rate,
            'stop_loss_rate': sl_rate,
            'first_trade': df['timestamp'].min(),
            'last_trade': df['timestamp'].max()
        }

    async def run_comprehensive_backtest(self) -> Dict:
        """運行全面回測"""
        logger.info("🚀 開始平衡版領先策略全面歷史回測")
        logger.info("=" * 100)
        logger.info("📋 回測配置:")
        logger.info("   - 策略數量: 19個活躍策略")
        logger.info("   - 回測期間: 2年歷史數據")
        logger.info("   - 數據源: Bybit價格 + Blave多空力道")
        logger.info("   - 策略邏輯: BB中軌突破 + TI動能確認 + RSI過濾")
        logger.info("   - 止盈止損: SUPERTREND動態計算")
        logger.info("=" * 100)

        all_results = []
        successful_backtests = 0

        for strategy_key, strategy_config in self.strategies.items():
            try:
                result = await self.backtest_single_strategy(strategy_key, strategy_config)
                if result:
                    all_results.append(result)
                    successful_backtests += 1

                    # 進度報告
                    logger.info(f"📊 進度: {successful_backtests}/{len(self.strategies)} 策略完成")

                # 避免過度請求API
                await asyncio.sleep(1)

            except Exception as e:
                logger.error(f"❌ {strategy_key} 回測異常: {e}")
                continue

        # 生成綜合報告
        if all_results:
            self.generate_comprehensive_report(all_results)
            self.save_results_to_files(all_results)

            logger.info(f"✅ 全面回測完成!")
            logger.info(f"📊 成功回測策略: {len(all_results)}/{len(self.strategies)}")
            logger.info(f"📈 總交易記錄: {len(self.trade_records)} 筆")
        else:
            logger.error("❌ 所有策略回測失敗")

        return {
            'results': all_results,
            'trade_records': self.trade_records,
            'summary': {
                'total_strategies': len(self.strategies),
                'successful_backtests': len(all_results),
                'total_trades': len(self.trade_records)
            }
        }

    def generate_comprehensive_report(self, results: List[Dict]):
        """生成綜合回測報告"""
        if not results:
            logger.error("❌ 無回測結果，無法生成報告")
            return

        print("\n" + "=" * 120)
        print("📊 平衡版領先策略 - 完整歷史回測報告")
        print("=" * 120)

        # 總體統計
        total_trades = sum(r['total_trades'] for r in results)
        total_strategies = len(results)
        avg_win_rate = np.mean([r['win_rate'] for r in results])
        avg_return = np.mean([r['total_return'] for r in results])
        avg_sharpe = np.mean([r['sharpe_ratio'] for r in results if not np.isnan(r['sharpe_ratio'])])

        print(f"\n🎯 總體績效摘要:")
        print(f"   測試策略數: {total_strategies}")
        print(f"   總交易筆數: {total_trades}")
        print(f"   平均勝率: {avg_win_rate:.1%}")
        print(f"   平均總回報: {avg_return:.2f}%")
        print(f"   平均夏普比率: {avg_sharpe:.2f}")

        # 按總回報排序
        results_by_return = sorted(results, key=lambda x: x['total_return'], reverse=True)

        print(f"\n🏆 策略表現排名 (按總回報排序):")
        print("-" * 120)

        ranking_data = []
        for i, result in enumerate(results_by_return, 1):
            ranking_data.append({
                '排名': i,
                '策略': result['strategy'],
                '幣種': result['symbol'],
                '時間框架': result['timeframe'],
                '交易數': result['total_trades'],
                '勝率': f"{result['win_rate']:.1%}",
                '總回報': f"{result['total_return']:.2f}%",
                '最大回撤': f"{result['max_drawdown']:.2f}%",
                '夏普比率': f"{result['sharpe_ratio']:.2f}",
                '盈利因子': f"{result['profit_factor']:.2f}",
                '信號頻率': f"{result['signal_frequency']:.2f}/天"
            })

        df_ranking = pd.DataFrame(ranking_data)
        print(df_ranking.to_string(index=False))

        # 按勝率排序
        results_by_winrate = sorted(results, key=lambda x: x['win_rate'], reverse=True)

        print(f"\n🎯 策略勝率排名:")
        print("-" * 120)

        winrate_data = []
        for i, result in enumerate(results_by_winrate[:10], 1):  # 顯示前10名
            winrate_data.append({
                '排名': i,
                '策略': result['strategy'],
                '勝率': f"{result['win_rate']:.1%}",
                '總回報': f"{result['total_return']:.2f}%",
                '交易數': result['total_trades'],
                '平均盈利': f"{result['avg_win']:.2f}%",
                '平均虧損': f"{result['avg_loss']:.2f}%"
            })

        df_winrate = pd.DataFrame(winrate_data)
        print(df_winrate.to_string(index=False))

        # 按夏普比率排序
        results_by_sharpe = sorted([r for r in results if not np.isnan(r['sharpe_ratio'])],
                                 key=lambda x: x['sharpe_ratio'], reverse=True)

        print(f"\n📈 策略夏普比率排名:")
        print("-" * 120)

        sharpe_data = []
        for i, result in enumerate(results_by_sharpe[:10], 1):  # 顯示前10名
            sharpe_data.append({
                '排名': i,
                '策略': result['strategy'],
                '夏普比率': f"{result['sharpe_ratio']:.2f}",
                '總回報': f"{result['total_return']:.2f}%",
                '勝率': f"{result['win_rate']:.1%}",
                '最大回撤': f"{result['max_drawdown']:.2f}%"
            })

        df_sharpe = pd.DataFrame(sharpe_data)
        print(df_sharpe.to_string(index=False))

        # 時間框架分析
        timeframe_stats = {}
        for result in results:
            tf = result['timeframe']
            if tf not in timeframe_stats:
                timeframe_stats[tf] = []
            timeframe_stats[tf].append(result)

        print(f"\n⏰ 時間框架分析:")
        print("-" * 80)

        for tf, tf_results in timeframe_stats.items():
            avg_return = np.mean([r['total_return'] for r in tf_results])
            avg_winrate = np.mean([r['win_rate'] for r in tf_results])
            avg_trades = np.mean([r['total_trades'] for r in tf_results])

            print(f"   {tf}: 平均回報 {avg_return:.2f}%, 平均勝率 {avg_winrate:.1%}, 平均交易數 {avg_trades:.0f}")

        # 策略改進效果分析
        print(f"\n💡 平衡版領先策略改進效果:")
        print("-" * 80)
        print(f"   ✅ BB中軌突破: 提前捕捉趨勢轉折，減少滯後性")
        print(f"   ✅ TI動能確認: 過濾假突破，提高信號質量")
        print(f"   ✅ RSI區間過濾: 避免極端區域開倉，降低風險")
        print(f"   ✅ 價格動能確認: 確保趨勢強度，提高成功率")
        print(f"   ✅ SUPERTREND止損: 動態調整止盈止損，優化風險收益比")

        # 風險提示
        print(f"\n⚠️ 風險提示:")
        print("-" * 80)
        print(f"   - 歷史績效不代表未來表現")
        print(f"   - 實盤交易需考慮滑點、手續費等成本")
        print(f"   - 建議分散投資，控制單筆交易風險")
        print(f"   - 定期檢視策略表現，適時調整參數")

    def save_results_to_files(self, results: List[Dict]):
        """保存結果到文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存詳細結果到JSON
            results_file = f"balanced_leading_backtest_results_{timestamp}.json"
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)

            # 保存交易記錄到CSV
            if self.trade_records:
                trades_file = f"balanced_leading_trade_records_{timestamp}.csv"
                df_trades = pd.DataFrame(self.trade_records)
                df_trades.to_csv(trades_file, index=False, encoding='utf-8-sig')
                logger.info(f"💾 交易記錄已保存: {trades_file}")

            # 保存績效摘要到CSV
            summary_file = f"balanced_leading_performance_summary_{timestamp}.csv"
            df_summary = pd.DataFrame(results)
            df_summary.to_csv(summary_file, index=False, encoding='utf-8-sig')

            logger.info(f"💾 回測結果已保存:")
            logger.info(f"   - 詳細結果: {results_file}")
            logger.info(f"   - 績效摘要: {summary_file}")

        except Exception as e:
            logger.error(f"❌ 保存結果失敗: {e}")

async def main():
    """主函數"""
    try:
        backtest_system = BalancedLeadingBacktester()

        logger.info("🚀 啟動平衡版領先策略完整歷史回測系統")
        logger.info(f"📊 將對 {len(backtest_system.strategies)} 個策略進行2年歷史回測")

        # 執行全面回測
        results = await backtest_system.run_comprehensive_backtest()

        if results['results']:
            logger.info("✅ 完整歷史回測系統執行完成")
            logger.info(f"📈 成功回測策略: {len(results['results'])}")
            logger.info(f"📊 總交易記錄: {len(results['trade_records'])}")
        else:
            logger.error("❌ 回測系統執行失敗")

    except Exception as e:
        logger.error(f"❌ 系統執行異常: {e}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    asyncio.run(main())
