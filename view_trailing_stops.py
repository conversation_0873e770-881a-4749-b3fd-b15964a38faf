#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
追蹤止損日誌查看工具
用於查看動態止損調整記錄和分析
"""

import pandas as pd
import os
from datetime import datetime, timedelta

def view_trailing_stops():
    """查看追蹤止損調整記錄"""
    csv_path = "data/trailing_stops_log.csv"
    
    if not os.path.exists(csv_path):
        print("📊 暫無追蹤止損記錄")
        return
    
    try:
        # 讀取CSV文件
        df = pd.read_csv(csv_path)
        
        if df.empty:
            print("📊 暫無追蹤止損記錄")
            return
        
        print("🎯 追蹤止損調整記錄")
        print("=" * 80)
        
        # 統計概覽
        total_adjustments = len(df)
        breakeven_adjustments = len(df[df['trigger_type'] == 'BREAKEVEN'])
        profit_lock_adjustments = len(df[df['trigger_type'] == 'PROFIT_LOCK'])
        unique_trades = df['trade_id'].nunique()
        
        print(f"📈 總調整次數: {total_adjustments}")
        print(f"🛡️ 保本線調整: {breakeven_adjustments}")
        print(f"🔒 利潤鎖定調整: {profit_lock_adjustments}")
        print(f"💼 涉及交易: {unique_trades}個")
        
        print("\n" + "=" * 80)
        
        # 按交易分組顯示
        print("\n🔍 詳細調整記錄:")
        for trade_id in df['trade_id'].unique():
            trade_adjustments = df[df['trade_id'] == trade_id].sort_values('timestamp')
            
            print(f"\n📊 {trade_id}:")
            for _, adj in trade_adjustments.iterrows():
                timestamp = datetime.fromisoformat(adj['timestamp'])
                trigger_emoji = "🛡️" if adj['trigger_type'] == 'BREAKEVEN' else "🔒"
                
                print(f"  {trigger_emoji} {timestamp.strftime('%m-%d %H:%M')}")
                print(f"     止損調整: ${float(adj['old_stop_loss']):.6f} → ${float(adj['new_stop_loss']):.6f}")
                print(f"     當前價格: ${float(adj['current_price']):.6f}")
                print(f"     利潤進展: {adj['profit_progress_pct']}")
                print(f"     原因: {adj['reason']}")
                print()
        
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 讀取追蹤止損記錄失敗: {e}")

def view_today_trailing_stops():
    """查看今日追蹤止損調整"""
    csv_path = "data/trailing_stops_log.csv"
    
    if not os.path.exists(csv_path):
        print("📊 暫無追蹤止損記錄")
        return
    
    try:
        df = pd.read_csv(csv_path)
        
        if df.empty:
            print("📊 暫無追蹤止損記錄")
            return
        
        # 轉換時間戳
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 篩選今日記錄
        today = datetime.now().date()
        today_adjustments = df[df['timestamp'].dt.date == today]
        
        if today_adjustments.empty:
            print(f"📊 今日({today})暫無追蹤止損調整")
            return
        
        print(f"🎯 今日追蹤止損調整報告 ({today})")
        print("=" * 60)
        
        for _, adj in today_adjustments.iterrows():
            trigger_emoji = "🛡️" if adj['trigger_type'] == 'BREAKEVEN' else "🔒"
            
            print(f"{trigger_emoji} {adj['trade_id']}")
            print(f"   時間: {adj['timestamp'].strftime('%H:%M:%S')}")
            print(f"   止損調整: ${float(adj['old_stop_loss']):.6f} → ${float(adj['new_stop_loss']):.6f}")
            print(f"   利潤進展: {adj['profit_progress_pct']}")
            print(f"   原因: {adj['reason']}")
            print()
        
        print("=" * 60)
        print(f"📈 今日統計:")
        print(f"   調整次數: {len(today_adjustments)}次")
        print(f"   保本線調整: {len(today_adjustments[today_adjustments['trigger_type'] == 'BREAKEVEN'])}次")
        print(f"   利潤鎖定: {len(today_adjustments[today_adjustments['trigger_type'] == 'PROFIT_LOCK'])}次")
        
    except Exception as e:
        print(f"❌ 生成今日報告失敗: {e}")

def analyze_trailing_stop_effectiveness():
    """分析追蹤止損效果"""
    trades_path = "data/trades_record.csv"
    trailing_path = "data/trailing_stops_log.csv"
    
    if not os.path.exists(trades_path) or not os.path.exists(trailing_path):
        print("📊 缺少必要的數據文件")
        return
    
    try:
        trades_df = pd.read_csv(trades_path)
        trailing_df = pd.read_csv(trailing_path)
        
        if trades_df.empty or trailing_df.empty:
            print("📊 暫無足夠數據進行分析")
            return
        
        print("📈 追蹤止損效果分析")
        print("=" * 60)
        
        # 找出有追蹤止損調整的交易
        adjusted_trades = trades_df[trades_df['trade_id'].isin(trailing_df['trade_id'].unique())]
        non_adjusted_trades = trades_df[~trades_df['trade_id'].isin(trailing_df['trade_id'].unique())]
        
        if not adjusted_trades.empty:
            adjusted_closed = adjusted_trades[adjusted_trades['status'] == 'CLOSED']
            if not adjusted_closed.empty:
                adjusted_closed['pnl_pct'] = pd.to_numeric(adjusted_closed['pnl_pct'], errors='coerce')
                avg_pnl_adjusted = adjusted_closed['pnl_pct'].mean()
                win_rate_adjusted = (adjusted_closed['pnl_pct'] > 0).mean() * 100
                
                print(f"🎯 有追蹤止損調整的交易:")
                print(f"   交易數量: {len(adjusted_closed)}")
                print(f"   平均盈虧: {avg_pnl_adjusted:+.2f}%")
                print(f"   勝率: {win_rate_adjusted:.1f}%")
        
        if not non_adjusted_trades.empty:
            non_adjusted_closed = non_adjusted_trades[non_adjusted_trades['status'] == 'CLOSED']
            if not non_adjusted_closed.empty:
                non_adjusted_closed['pnl_pct'] = pd.to_numeric(non_adjusted_closed['pnl_pct'], errors='coerce')
                avg_pnl_non_adjusted = non_adjusted_closed['pnl_pct'].mean()
                win_rate_non_adjusted = (non_adjusted_closed['pnl_pct'] > 0).mean() * 100
                
                print(f"\n📊 無追蹤止損調整的交易:")
                print(f"   交易數量: {len(non_adjusted_closed)}")
                print(f"   平均盈虧: {avg_pnl_non_adjusted:+.2f}%")
                print(f"   勝率: {win_rate_non_adjusted:.1f}%")
        
        print("\n" + "=" * 60)
        
    except Exception as e:
        print(f"❌ 分析失敗: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "today":
            view_today_trailing_stops()
        elif sys.argv[1] == "analyze":
            analyze_trailing_stop_effectiveness()
        else:
            print("用法:")
            print("  python view_trailing_stops.py        # 查看所有記錄")
            print("  python view_trailing_stops.py today  # 查看今日記錄")
            print("  python view_trailing_stops.py analyze # 效果分析")
    else:
        view_trailing_stops()
