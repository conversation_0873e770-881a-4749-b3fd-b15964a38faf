#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
止盈止損方法研究模塊
實現多種止盈止損策略並進行系統化測試
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import logging
from dataclasses import dataclass
from enum import Enum

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class StopLossType(Enum):
    """止損類型枚舉"""
    ATR = "ATR"
    VWAP = "VWAP"
    BOLLINGER = "BOLLINGER"
    SAR = "SAR"
    SUPERTREND = "SUPERTREND"
    SUPPORT_RESISTANCE = "SUPPORT_RESISTANCE"
    MULTI_INDICATOR = "MULTI_INDICATOR"

@dataclass
class StopLossResult:
    """止損結果數據類"""
    stop_loss_price: float
    take_profit_price: float
    method: StopLossType
    confidence: float
    reason: str
    risk_reward_ratio: float

class TechnicalIndicators:
    """技術指標計算類"""

    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """計算ATR (Average True Range)"""
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return tr.rolling(window=period).mean()

    @staticmethod
    def rsi(close: pd.Series, period: int = 14) -> pd.Series:
        """計算RSI (Relative Strength Index)"""
        delta = close.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))

    @staticmethod
    def ema(close: pd.Series, period: int) -> pd.Series:
        """計算EMA (Exponential Moving Average)"""
        return close.ewm(span=period).mean()

    @staticmethod
    def bollinger_bands(close: pd.Series, period: int = 20, std_dev: float = 2.0) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """計算Bollinger Bands"""
        sma = close.rolling(window=period).mean()
        std = close.rolling(window=period).std()
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        return upper, sma, lower

    @staticmethod
    def sar(high: pd.Series, low: pd.Series, acceleration: float = 0.02, maximum: float = 0.2) -> pd.Series:
        """計算SAR (Parabolic Stop and Reverse)"""
        # 簡化的SAR計算
        sar = pd.Series(index=high.index, dtype=float)
        af = acceleration
        ep = high.iloc[0]
        sar.iloc[0] = low.iloc[0]

        for i in range(1, len(high)):
            if i == 1:
                sar.iloc[i] = sar.iloc[i-1] + af * (ep - sar.iloc[i-1])
            else:
                sar.iloc[i] = sar.iloc[i-1] + af * (ep - sar.iloc[i-1])

            # 簡化邏輯，實際SAR計算更複雜
            if high.iloc[i] > ep:
                ep = high.iloc[i]
                af = min(af + acceleration, maximum)

        return sar

class StopLossTakeProfitCalculator:
    """止盈止損計算器"""

    def __init__(self):
        self.logger = logger
        self.indicators = TechnicalIndicators()
        
    def calculate_atr_stop_loss(self, df: pd.DataFrame, direction: str, 
                               entry_price: float, atr_multiplier: float = 2.0,
                               profit_multiplier: float = 3.0) -> StopLossResult:
        """
        ATR基準止盈止損
        
        Args:
            df: 價格數據
            direction: 交易方向 ('LONG' or 'SHORT')
            entry_price: 入場價格
            atr_multiplier: ATR止損倍數
            profit_multiplier: 止盈倍數
        """
        try:
            # 計算ATR
            atr = self.indicators.atr(df['high'], df['low'], df['close'], period=14)
            current_atr = atr.iloc[-1]
            
            if direction == 'LONG':
                stop_loss = entry_price - (current_atr * atr_multiplier)
                take_profit = entry_price + (current_atr * profit_multiplier)
            else:  # SHORT
                stop_loss = entry_price + (current_atr * atr_multiplier)
                take_profit = entry_price - (current_atr * profit_multiplier)
            
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward_ratio = reward / risk if risk > 0 else 0
            
            return StopLossResult(
                stop_loss_price=stop_loss,
                take_profit_price=take_profit,
                method=StopLossType.ATR,
                confidence=0.7,
                reason=f"ATR({current_atr:.6f}) * {atr_multiplier}倍止損, {profit_multiplier}倍止盈",
                risk_reward_ratio=risk_reward_ratio
            )
            
        except Exception as e:
            self.logger.error(f"ATR止盈止損計算失敗: {e}")
            return None
    
    def calculate_vwap_stop_loss(self, df: pd.DataFrame, direction: str,
                                entry_price: float, vwap_deviation: float = 0.02) -> StopLossResult:
        """
        VWAP基準止盈止損
        
        Args:
            df: 價格數據（需包含volume）
            direction: 交易方向
            entry_price: 入場價格
            vwap_deviation: VWAP偏離度
        """
        try:
            # 計算VWAP
            typical_price = (df['high'] + df['low'] + df['close']) / 3
            vwap = (typical_price * df['volume']).cumsum() / df['volume'].cumsum()
            current_vwap = vwap.iloc[-1]
            
            # 計算VWAP標準差
            vwap_std = ((typical_price - vwap) ** 2 * df['volume']).cumsum() / df['volume'].cumsum()
            vwap_std = np.sqrt(vwap_std.iloc[-1])
            
            if direction == 'LONG':
                stop_loss = current_vwap - (vwap_std * 1.5)
                take_profit = current_vwap + (vwap_std * 2.5)
            else:  # SHORT
                stop_loss = current_vwap + (vwap_std * 1.5)
                take_profit = current_vwap - (vwap_std * 2.5)
            
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward_ratio = reward / risk if risk > 0 else 0
            
            return StopLossResult(
                stop_loss_price=stop_loss,
                take_profit_price=take_profit,
                method=StopLossType.VWAP,
                confidence=0.75,
                reason=f"VWAP({current_vwap:.6f}) ± {vwap_std:.6f}標準差",
                risk_reward_ratio=risk_reward_ratio
            )
            
        except Exception as e:
            self.logger.error(f"VWAP止盈止損計算失敗: {e}")
            return None
    
    def calculate_bollinger_stop_loss(self, df: pd.DataFrame, direction: str,
                                     entry_price: float, bb_period: int = 20,
                                     bb_std: float = 2.0) -> StopLossResult:
        """
        Bollinger Bands基準止盈止損
        
        Args:
            df: 價格數據
            direction: 交易方向
            entry_price: 入場價格
            bb_period: 布林帶週期
            bb_std: 布林帶標準差倍數
        """
        try:
            # 計算Bollinger Bands
            upper, middle, lower = self.indicators.bollinger_bands(
                df['close'],
                period=bb_period,
                std_dev=bb_std
            )

            current_upper = upper.iloc[-1]
            current_middle = middle.iloc[-1]
            current_lower = lower.iloc[-1]
            
            if direction == 'LONG':
                stop_loss = current_lower
                take_profit = current_upper + (current_upper - current_middle)
            else:  # SHORT
                stop_loss = current_upper
                take_profit = current_lower - (current_middle - current_lower)
            
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward_ratio = reward / risk if risk > 0 else 0
            
            return StopLossResult(
                stop_loss_price=stop_loss,
                take_profit_price=take_profit,
                method=StopLossType.BOLLINGER,
                confidence=0.8,
                reason=f"布林帶: 上軌{current_upper:.6f}, 中軌{current_middle:.6f}, 下軌{current_lower:.6f}",
                risk_reward_ratio=risk_reward_ratio
            )
            
        except Exception as e:
            self.logger.error(f"Bollinger Bands止盈止損計算失敗: {e}")
            return None
    
    def calculate_sar_stop_loss(self, df: pd.DataFrame, direction: str,
                               entry_price: float, acceleration: float = 0.02,
                               maximum: float = 0.2) -> StopLossResult:
        """
        SAR (Parabolic Stop and Reverse) 止盈止損
        
        Args:
            df: 價格數據
            direction: 交易方向
            entry_price: 入場價格
            acceleration: 加速因子
            maximum: 最大加速因子
        """
        try:
            # 計算SAR
            sar = self.indicators.sar(df['high'], df['low'],
                                    acceleration=acceleration, maximum=maximum)
            current_sar = sar.iloc[-1]
            
            # SAR作為動態止損
            if direction == 'LONG':
                stop_loss = current_sar
                # 基於ATR計算止盈
                atr = self.indicators.atr(df['high'], df['low'], df['close'], period=14)
                take_profit = entry_price + (atr.iloc[-1] * 3.0)
            else:  # SHORT
                stop_loss = current_sar
                atr = self.indicators.atr(df['high'], df['low'], df['close'], period=14)
                take_profit = entry_price - (atr.iloc[-1] * 3.0)
            
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward_ratio = reward / risk if risk > 0 else 0
            
            return StopLossResult(
                stop_loss_price=stop_loss,
                take_profit_price=take_profit,
                method=StopLossType.SAR,
                confidence=0.72,
                reason=f"SAR動態止損: {current_sar:.6f}",
                risk_reward_ratio=risk_reward_ratio
            )
            
        except Exception as e:
            self.logger.error(f"SAR止盈止損計算失敗: {e}")
            return None
    
    def calculate_supertrend_stop_loss(self, df: pd.DataFrame, direction: str,
                                      entry_price: float, period: int = 10,
                                      multiplier: float = 3.0) -> StopLossResult:
        """
        Supertrend指標止盈止損
        
        Args:
            df: 價格數據
            direction: 交易方向
            entry_price: 入場價格
            period: ATR週期
            multiplier: ATR倍數
        """
        try:
            # 計算Supertrend
            hl2 = (df['high'] + df['low']) / 2
            atr = self.indicators.atr(df['high'], df['low'], df['close'], period=period)
            
            upper_band = hl2 + (multiplier * atr)
            lower_band = hl2 - (multiplier * atr)
            
            # 簡化的Supertrend計算
            supertrend = np.where(df['close'] <= lower_band.shift(1), lower_band, 
                                 np.where(df['close'] >= upper_band.shift(1), upper_band, np.nan))
            
            # 前向填充
            supertrend = pd.Series(supertrend).fillna(method='ffill')
            current_supertrend = supertrend.iloc[-1]
            
            if direction == 'LONG':
                stop_loss = current_supertrend
                take_profit = entry_price + (abs(entry_price - current_supertrend) * 2.5)
            else:  # SHORT
                stop_loss = current_supertrend
                take_profit = entry_price - (abs(entry_price - current_supertrend) * 2.5)
            
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward_ratio = reward / risk if risk > 0 else 0
            
            return StopLossResult(
                stop_loss_price=stop_loss,
                take_profit_price=take_profit,
                method=StopLossType.SUPERTREND,
                confidence=0.78,
                reason=f"Supertrend動態止損: {current_supertrend:.6f}",
                risk_reward_ratio=risk_reward_ratio
            )
            
        except Exception as e:
            self.logger.error(f"Supertrend止盈止損計算失敗: {e}")
            return None

    def calculate_support_resistance_stop_loss(self, df: pd.DataFrame, direction: str,
                                              entry_price: float, lookback: int = 20) -> StopLossResult:
        """
        支撐阻力位基準止盈止損

        Args:
            df: 價格數據
            direction: 交易方向
            entry_price: 入場價格
            lookback: 回看週期
        """
        try:
            # 計算支撐阻力位
            highs = df['high'].rolling(window=lookback).max()
            lows = df['low'].rolling(window=lookback).min()

            # 找到最近的支撐阻力位
            recent_high = highs.iloc[-1]
            recent_low = lows.iloc[-1]

            # 計算多個時間框架的支撐阻力位
            pivot_highs = []
            pivot_lows = []

            for i in range(lookback, len(df)):
                if df['high'].iloc[i] == df['high'].iloc[i-lookback:i+1].max():
                    pivot_highs.append(df['high'].iloc[i])
                if df['low'].iloc[i] == df['low'].iloc[i-lookback:i+1].min():
                    pivot_lows.append(df['low'].iloc[i])

            if direction == 'LONG':
                # 止損設在最近支撐位下方
                stop_loss = recent_low * 0.995  # 0.5%緩衝
                # 止盈設在最近阻力位
                take_profit = recent_high * 1.005  # 0.5%緩衝
            else:  # SHORT
                # 止損設在最近阻力位上方
                stop_loss = recent_high * 1.005
                # 止盈設在最近支撐位
                take_profit = recent_low * 0.995

            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward_ratio = reward / risk if risk > 0 else 0

            return StopLossResult(
                stop_loss_price=stop_loss,
                take_profit_price=take_profit,
                method=StopLossType.SUPPORT_RESISTANCE,
                confidence=0.85,
                reason=f"支撐位{recent_low:.6f}, 阻力位{recent_high:.6f}",
                risk_reward_ratio=risk_reward_ratio
            )

        except Exception as e:
            self.logger.error(f"支撐阻力位止盈止損計算失敗: {e}")
            return None

    def calculate_multi_indicator_stop_loss(self, df: pd.DataFrame, direction: str,
                                           entry_price: float,
                                           trend_weight: float = 0.4,
                                           momentum_weight: float = 0.3,
                                           volatility_weight: float = 0.3) -> StopLossResult:
        """
        多指標綜合確認止盈止損

        Args:
            df: 價格數據
            direction: 交易方向
            entry_price: 入場價格
            trend_weight: 趨勢指標權重
            momentum_weight: 動量指標權重
            volatility_weight: 波動性指標權重
        """
        try:
            # 1. 趨勢指標 - EMA
            ema_20 = self.indicators.ema(df['close'], period=20)
            ema_50 = self.indicators.ema(df['close'], period=50)
            trend_signal = 1 if ema_20.iloc[-1] > ema_50.iloc[-1] else -1

            # 2. 動量指標 - RSI
            rsi = self.indicators.rsi(df['close'], period=14)
            momentum_signal = 1 if rsi.iloc[-1] < 70 else -1  # 避免超買

            # 3. 波動性指標 - ATR
            atr = self.indicators.atr(df['high'], df['low'], df['close'], period=14)
            volatility_multiplier = 2.0 if atr.iloc[-1] > atr.iloc[-20:].mean() else 1.5

            # 綜合信號強度
            signal_strength = (trend_signal * trend_weight +
                             momentum_signal * momentum_weight +
                             volatility_multiplier * volatility_weight / 2.0)

            # 根據信號強度調整止盈止損
            base_atr_multiplier = 2.0
            profit_multiplier = 3.0

            # 信號越強，止損越緊，止盈越遠
            if abs(signal_strength) > 0.7:
                atr_multiplier = base_atr_multiplier * 0.8
                profit_multiplier = profit_multiplier * 1.2
                confidence = 0.9
            elif abs(signal_strength) > 0.4:
                atr_multiplier = base_atr_multiplier
                confidence = 0.75
            else:
                atr_multiplier = base_atr_multiplier * 1.2
                profit_multiplier = profit_multiplier * 0.9
                confidence = 0.6

            if direction == 'LONG':
                stop_loss = entry_price - (atr.iloc[-1] * atr_multiplier)
                take_profit = entry_price + (atr.iloc[-1] * profit_multiplier)
            else:  # SHORT
                stop_loss = entry_price + (atr.iloc[-1] * atr_multiplier)
                take_profit = entry_price - (atr.iloc[-1] * profit_multiplier)

            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward_ratio = reward / risk if risk > 0 else 0

            return StopLossResult(
                stop_loss_price=stop_loss,
                take_profit_price=take_profit,
                method=StopLossType.MULTI_INDICATOR,
                confidence=confidence,
                reason=f"多指標綜合: 趨勢{trend_signal}, 動量{momentum_signal:.0f}, 波動{volatility_multiplier:.1f}, 強度{signal_strength:.2f}",
                risk_reward_ratio=risk_reward_ratio
            )

        except Exception as e:
            self.logger.error(f"多指標綜合止盈止損計算失敗: {e}")
            return None

    def get_all_stop_loss_methods(self, df: pd.DataFrame, direction: str,
                                 entry_price: float) -> List[StopLossResult]:
        """
        獲取所有止盈止損方法的結果

        Args:
            df: 價格數據
            direction: 交易方向
            entry_price: 入場價格

        Returns:
            所有方法的結果列表
        """
        methods = []

        # ATR方法
        atr_result = self.calculate_atr_stop_loss(df, direction, entry_price)
        if atr_result:
            methods.append(atr_result)

        # VWAP方法（需要volume數據）
        if 'volume' in df.columns:
            vwap_result = self.calculate_vwap_stop_loss(df, direction, entry_price)
            if vwap_result:
                methods.append(vwap_result)

        # Bollinger Bands方法
        bb_result = self.calculate_bollinger_stop_loss(df, direction, entry_price)
        if bb_result:
            methods.append(bb_result)

        # SAR方法
        sar_result = self.calculate_sar_stop_loss(df, direction, entry_price)
        if sar_result:
            methods.append(sar_result)

        # Supertrend方法
        supertrend_result = self.calculate_supertrend_stop_loss(df, direction, entry_price)
        if supertrend_result:
            methods.append(supertrend_result)

        # 支撐阻力位方法
        sr_result = self.calculate_support_resistance_stop_loss(df, direction, entry_price)
        if sr_result:
            methods.append(sr_result)

        # 多指標綜合方法
        multi_result = self.calculate_multi_indicator_stop_loss(df, direction, entry_price)
        if multi_result:
            methods.append(multi_result)

        return methods

    def recommend_best_method(self, methods: List[StopLossResult],
                             preference: str = "balanced") -> StopLossResult:
        """
        推薦最佳止盈止損方法

        Args:
            methods: 所有方法結果
            preference: 偏好類型 ("conservative", "aggressive", "balanced")

        Returns:
            推薦的最佳方法
        """
        if not methods:
            return None

        if preference == "conservative":
            # 保守型：選擇風險回報比適中且信心度高的方法
            scored_methods = [(m, m.confidence * 0.6 + min(m.risk_reward_ratio, 3.0) * 0.4)
                            for m in methods]
        elif preference == "aggressive":
            # 激進型：選擇風險回報比高的方法
            scored_methods = [(m, m.risk_reward_ratio * 0.7 + m.confidence * 0.3)
                            for m in methods]
        else:  # balanced
            # 平衡型：綜合考慮各項指標
            scored_methods = [(m, m.confidence * 0.4 + min(m.risk_reward_ratio, 4.0) * 0.4 +
                             (1.0 if m.method == StopLossType.MULTI_INDICATOR else 0.8) * 0.2)
                            for m in methods]

        # 按得分排序
        scored_methods.sort(key=lambda x: x[1], reverse=True)

        return scored_methods[0][0] if scored_methods else None
