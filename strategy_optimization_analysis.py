#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OBV+TI+BB策略優化分析
分析回測結果並生成優化建議
"""

import json
import pandas as pd
from datetime import datetime

def analyze_obv_ti_bb_strategies():
    """分析OBV+TI+BB策略回測結果"""
    
    print("🔍 OBV+TI+BB策略優化分析")
    print("=" * 60)
    
    # 讀取回測結果
    with open('improved_strategies_results_20250717_032806.json', 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    # 篩選OBV+TI+BB策略結果
    strategy3_results = [r for r in results if r['strategy_method'] == 'strategy3']
    
    print(f"📊 總策略數: {len(strategy3_results)}")
    
    # 分類有效和無效策略
    effective_strategies = []
    ineffective_strategies = []
    
    for result in strategy3_results:
        strategy_key = result['strategy']
        total_trades = result['total_trades']
        total_return = result['total_return']
        win_rate = result['win_rate']
        sharpe_ratio = result.get('sharpe_ratio', 0)
        
        # 判斷策略是否有效：有交易信號且總回報為正
        if total_trades > 0 and total_return > 0:
            effective_strategies.append(result)
        else:
            ineffective_strategies.append(result)
    
    print(f"\n✅ 有效策略: {len(effective_strategies)}")
    print(f"❌ 無效策略: {len(ineffective_strategies)}")
    print(f"📈 優化比例: {len(effective_strategies)}/{len(strategy3_results)} = {len(effective_strategies)/len(strategy3_results)*100:.1f}%")
    
    # 詳細分析有效策略
    print(f"\n🎯 有效策略詳細分析:")
    print("-" * 80)
    print(f"{'策略':<15} {'交易數':<8} {'勝率':<8} {'總回報':<10} {'夏普比率':<10} {'信號頻率':<10}")
    print("-" * 80)
    
    total_trades_sum = 0
    total_return_sum = 0
    win_rate_sum = 0
    sharpe_sum = 0
    signal_freq_sum = 0
    
    for strategy in sorted(effective_strategies, key=lambda x: x['total_return'], reverse=True):
        strategy_key = strategy['strategy']
        total_trades = strategy['total_trades']
        win_rate = strategy['win_rate']
        total_return = strategy['total_return']
        sharpe_ratio = strategy.get('sharpe_ratio', 0)
        signal_frequency = strategy.get('signal_frequency', 0)
        
        print(f"{strategy_key:<15} {total_trades:<8} {win_rate:<8.1%} {total_return:<10.2f}% {sharpe_ratio:<10.3f} {signal_frequency:<10.2f}")
        
        total_trades_sum += total_trades
        total_return_sum += total_return
        win_rate_sum += win_rate
        sharpe_sum += sharpe_ratio
        signal_freq_sum += signal_frequency
    
    # 計算平均值
    n_effective = len(effective_strategies)
    avg_win_rate = win_rate_sum / n_effective
    avg_return = total_return_sum / n_effective
    avg_sharpe = sharpe_sum / n_effective
    avg_signal_freq = signal_freq_sum / n_effective
    
    print("-" * 80)
    print(f"{'平均值':<15} {total_trades_sum:<8} {avg_win_rate:<8.1%} {avg_return:<10.2f}% {avg_sharpe:<10.3f} {avg_signal_freq:<10.2f}")
    
    # 分析無效策略
    print(f"\n❌ 無效策略分析:")
    print("-" * 60)
    print(f"{'策略':<15} {'交易數':<8} {'總回報':<10} {'原因':<15}")
    print("-" * 60)
    
    for strategy in ineffective_strategies:
        strategy_key = strategy['strategy']
        total_trades = strategy['total_trades']
        total_return = strategy['total_return']
        
        if total_trades == 0:
            reason = "無交易信號"
        elif total_return <= 0:
            reason = "負收益"
        else:
            reason = "其他"
        
        print(f"{strategy_key:<15} {total_trades:<8} {total_return:<10.2f}% {reason:<15}")
    
    # 生成優化建議
    print(f"\n💡 優化建議:")
    print("1. 系統性能優化:")
    print(f"   - 移除 {len(ineffective_strategies)} 個無效策略，減少 {len(ineffective_strategies)/len(strategy3_results)*100:.1f}% 計算負擔")
    print(f"   - 保留 {len(effective_strategies)} 個有效策略，專注於高質量信號")
    
    print("\n2. 資源分配優化:")
    print(f"   - 掃描時間可減少約 {len(ineffective_strategies)/len(strategy3_results)*100:.1f}%")
    print(f"   - API調用次數減少 {len(ineffective_strategies)} 次/輪")
    print(f"   - 內存使用優化，減少無用數據處理")
    
    print("\n3. 策略質量提升:")
    print(f"   - 平均勝率從混合提升到 {avg_win_rate:.1%}")
    print(f"   - 平均回報提升到 {avg_return:.2f}%")
    print(f"   - 平均夏普比率: {avg_sharpe:.3f}")
    
    # 生成配置文件建議
    print(f"\n📋 配置文件優化:")
    effective_config = {}
    for strategy in effective_strategies:
        strategy_key = strategy['strategy']
        effective_config[strategy_key] = {
            "symbol": strategy['symbol'],
            "timeframe": strategy['timeframe'],
            "enabled": True,
            "bb_window": 20,
            "bb_std": 2.0,
            "ti_lookback": 24,
            "obv_period": 10,
            "backtest_verified": True,
            "performance": {
                "total_trades": strategy['total_trades'],
                "win_rate": strategy['win_rate'],
                "total_return": strategy['total_return'],
                "sharpe_ratio": strategy.get('sharpe_ratio', 0),
                "signal_frequency": strategy.get('signal_frequency', 0)
            }
        }
    
    # 保存優化配置
    optimized_config = {
        "system_config": {
            "scan_interval": 60,
            "max_concurrent_trades_per_symbol": 1,
            "enable_telegram_notifications": True,
            "optimization_date": datetime.now().isoformat(),
            "optimization_note": f"Optimized from {len(strategy3_results)} to {len(effective_strategies)} strategies"
        },
        "active_strategies": effective_config,
        "optimization_summary": {
            "original_strategies": len(strategy3_results),
            "effective_strategies": len(effective_strategies),
            "removed_strategies": len(ineffective_strategies),
            "optimization_ratio": f"{len(effective_strategies)/len(strategy3_results)*100:.1f}%",
            "avg_win_rate": avg_win_rate,
            "avg_return": avg_return,
            "avg_sharpe_ratio": avg_sharpe,
            "avg_signal_frequency": avg_signal_freq
        }
    }
    
    # 保存到文件
    with open('obv_ti_bb_final_optimized_config.json', 'w', encoding='utf-8') as f:
        json.dump(optimized_config, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"✅ 優化配置已保存到: obv_ti_bb_final_optimized_config.json")
    
    return effective_strategies, ineffective_strategies

if __name__ == "__main__":
    try:
        effective, ineffective = analyze_obv_ti_bb_strategies()
        
        print(f"\n🎉 優化完成!")
        print(f"📊 有效策略: {len(effective)}")
        print(f"❌ 移除策略: {len(ineffective)}")
        print(f"💾 配置文件: obv_ti_bb_final_optimized_config.json")
        
    except Exception as e:
        print(f"❌ 分析失敗: {e}")
        import traceback
        traceback.print_exc()
