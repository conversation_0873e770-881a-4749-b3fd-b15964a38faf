#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真實數據回測系統
使用真實的Bybit歷史數據和Blave API數據進行止盈止損方法回測
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os
from typing import Dict, List, Optional, Tuple
import logging

from stop_loss_take_profit_research import StopLossTakeProfitCalculator, StopLossResult, StopLossType

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealDataBacktester:
    """真實數據回測器"""
    
    def __init__(self):
        self.calculator = StopLossTakeProfitCalculator()
        self.session = None
        
        # API配置
        self.bybit_base_url = "https://api.bybit.com"
        self.blave_base_url = "https://api.blave.org"
        self.blave_api_key = "acf05af3b4a4cd8a0cad993c3588dfdd3117ca569a963be44cf89044d64f41a6"
        
        # 測試幣種和時間框架
        self.test_symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', '1000PEPEUSDT']
        self.test_timeframes = ['1h', '4h']
        
        # 結果存儲
        self.results_dir = "real_backtest_results"
        os.makedirs(self.results_dir, exist_ok=True)
    
    async def __aenter__(self):
        """異步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """異步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def get_bybit_historical_data(self, symbol: str, timeframe: str, 
                                       days: int = 30) -> Optional[pd.DataFrame]:
        """獲取Bybit歷史數據"""
        try:
            # 時框映射
            interval_map = {
                '1h': '60',
                '4h': '240',
                '1H': '60',
                '4H': '240'
            }
            
            interval = interval_map.get(timeframe, '60')
            
            # 計算時間範圍
            end_time = int(datetime.now().timestamp() * 1000)
            start_time = int((datetime.now() - timedelta(days=days)).timestamp() * 1000)
            
            all_data = []
            current_end = end_time
            
            # 分批獲取數據（Bybit限制每次最多1000條）
            target_records = days * 24 if timeframe in ['1h', '1H'] else days * 6  # 1h或4h

            while len(all_data) < target_records:
                url = f"{self.bybit_base_url}/v5/market/kline"
                params = {
                    "category": "linear",
                    "symbol": symbol,
                    "interval": interval,
                    "limit": 1000,
                    "end": current_end
                }
                
                async with self.session.get(url, params=params, timeout=30) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if data.get("retCode") == 0:
                            kline_data = data.get("result", {}).get("list", [])
                            
                            if not kline_data:
                                break
                            
                            all_data.extend(kline_data)
                            
                            # 更新結束時間為最早的時間戳
                            current_end = int(kline_data[-1][0]) - 1
                            
                            # 如果獲取的數據時間戳小於開始時間，停止
                            if int(kline_data[-1][0]) < start_time:
                                break

                            # 如果獲取的數據少於請求的數量，說明沒有更多數據了
                            if len(kline_data) < 1000:
                                break
                        else:
                            logger.error(f"Bybit API錯誤: {data}")
                            break
                    else:
                        logger.error(f"HTTP錯誤: {response.status}")
                        break
                
                # 避免請求過於頻繁
                await asyncio.sleep(0.1)
            
            if not all_data:
                return None
            
            # 轉換為DataFrame
            df = pd.DataFrame(all_data)
            df.columns = ["timestamp", "open", "high", "low", "close", "volume", "turnover"]
            
            # 數據類型轉換
            for col in ["open", "high", "low", "close", "volume"]:
                df[col] = pd.to_numeric(df[col])
            
            df["timestamp"] = pd.to_datetime(df["timestamp"].astype(float), unit="ms")
            df = df.sort_values("timestamp").reset_index(drop=True)
            df.set_index("timestamp", inplace=True)
            
            # 過濾時間範圍
            start_dt = datetime.fromtimestamp(start_time / 1000)
            df = df[df.index >= start_dt]
            
            logger.info(f"✅ 獲取 {symbol} {timeframe} 數據: {len(df)} 條記錄")
            return df
            
        except Exception as e:
            logger.error(f"獲取 {symbol} {timeframe} 數據失敗: {e}")
            return None
    
    async def get_blave_taker_intensity(self, symbol: str, timeframe: str, 
                                       start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """獲取Blave Taker Intensity數據"""
        try:
            # 符號映射
            symbol_map = {
                '1000PEPEUSDT': '1000PEPE',
                'BTCUSDT': 'BTC',
                'ETHUSDT': 'ETH',
                'SOLUSDT': 'SOL',
                'XRPUSDT': 'XRP'
            }
            
            blave_symbol = symbol_map.get(symbol, symbol.replace('USDT', ''))
            
            url = f"{self.blave_base_url}/taker_intensity/get_alpha"
            params = {
                "symbol": blave_symbol,
                "period": "24",
                "start_date": start_date,
                "end_date": end_date,
                "timeframe": timeframe.lower()
            }
            
            headers = {
                "X-API-KEY": self.blave_api_key
            }
            
            async with self.session.get(url, params=params, headers=headers, timeout=30) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if data.get("success") and data.get("data"):
                        df = pd.DataFrame(data["data"])
                        df['timestamp'] = pd.to_datetime(df['timestamp'])
                        df.set_index('timestamp', inplace=True)
                        
                        logger.info(f"✅ 獲取 {symbol} Taker Intensity數據: {len(df)} 條記錄")
                        return df
                    else:
                        logger.warning(f"Blave API返回空數據: {symbol}")
                        return None
                else:
                    logger.error(f"Blave API錯誤: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"獲取 {symbol} Taker Intensity數據失敗: {e}")
            return None
    
    def generate_rsi_signals(self, df: pd.DataFrame, rsi_period: int = 14,
                           long_threshold: int = 70, short_threshold: int = 30) -> List[Tuple[int, str]]:
        """
        基於RSI生成交易信號（模擬原始策略的信號生成邏輯）
        
        Args:
            df: 價格數據
            rsi_period: RSI週期
            long_threshold: 多頭閾值
            short_threshold: 空頭閾值
            
        Returns:
            (索引, 方向) 的信號列表
        """
        signals = []
        
        # 計算RSI
        rsi = self.calculator.indicators.rsi(df['close'], period=rsi_period)
        
        # 計算其他指標用於信號確認
        bb_upper, bb_middle, bb_lower = self.calculator.indicators.bollinger_bands(df['close'])
        atr = self.calculator.indicators.atr(df['high'], df['low'], df['close'])
        
        # 從足夠的數據開始檢查信號
        start_idx = max(rsi_period, 20)
        
        for i in range(start_idx, len(df) - 5):  # 留出足夠空間執行交易
            current_rsi = rsi.iloc[i]
            current_price = df['close'].iloc[i]
            
            # 跳過無效數據
            if pd.isna(current_rsi) or pd.isna(bb_upper.iloc[i]):
                continue
            
            # 多頭信號條件
            if (current_rsi >= long_threshold and 
                current_price > bb_middle.iloc[i] and
                df['volume'].iloc[i] > df['volume'].iloc[i-5:i].mean()):
                signals.append((i, 'LONG'))
            
            # 空頭信號條件  
            elif (current_rsi <= short_threshold and 
                  current_price < bb_middle.iloc[i] and
                  df['volume'].iloc[i] > df['volume'].iloc[i-5:i].mean()):
                signals.append((i, 'SHORT'))
        
        return signals
    
    def simulate_trade(self, df: pd.DataFrame, entry_idx: int, direction: str,
                      stop_loss_result: StopLossResult, max_holding_periods: int = 168) -> Dict:
        """
        模擬交易執行
        
        Args:
            df: 價格數據
            entry_idx: 入場索引
            direction: 交易方向
            stop_loss_result: 止盈止損結果
            max_holding_periods: 最大持倉週期
            
        Returns:
            交易結果字典
        """
        entry_price = df['close'].iloc[entry_idx]
        entry_time = df.index[entry_idx]
        stop_loss_price = stop_loss_result.stop_loss_price
        take_profit_price = stop_loss_result.take_profit_price
        
        # 從入場後開始檢查
        end_idx = min(entry_idx + max_holding_periods, len(df) - 1)
        
        for i in range(entry_idx + 1, end_idx + 1):
            current_high = df['high'].iloc[i]
            current_low = df['low'].iloc[i]
            current_time = df.index[i]
            
            if direction == 'LONG':
                # 檢查止損
                if current_low <= stop_loss_price:
                    exit_price = stop_loss_price
                    pnl_pct = ((exit_price - entry_price) / entry_price) * 100
                    return {
                        'entry_time': entry_time,
                        'exit_time': current_time,
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'exit_type': 'STOP_LOSS',
                        'pnl_pct': pnl_pct,
                        'holding_periods': i - entry_idx,
                        'method': stop_loss_result.method.value,
                        'risk_reward_ratio': stop_loss_result.risk_reward_ratio,
                        'confidence': stop_loss_result.confidence
                    }
                
                # 檢查止盈
                if current_high >= take_profit_price:
                    exit_price = take_profit_price
                    pnl_pct = ((exit_price - entry_price) / entry_price) * 100
                    return {
                        'entry_time': entry_time,
                        'exit_time': current_time,
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'exit_type': 'TAKE_PROFIT',
                        'pnl_pct': pnl_pct,
                        'holding_periods': i - entry_idx,
                        'method': stop_loss_result.method.value,
                        'risk_reward_ratio': stop_loss_result.risk_reward_ratio,
                        'confidence': stop_loss_result.confidence
                    }
            
            else:  # SHORT
                # 檢查止損
                if current_high >= stop_loss_price:
                    exit_price = stop_loss_price
                    pnl_pct = ((entry_price - exit_price) / entry_price) * 100
                    return {
                        'entry_time': entry_time,
                        'exit_time': current_time,
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'exit_type': 'STOP_LOSS',
                        'pnl_pct': pnl_pct,
                        'holding_periods': i - entry_idx,
                        'method': stop_loss_result.method.value,
                        'risk_reward_ratio': stop_loss_result.risk_reward_ratio,
                        'confidence': stop_loss_result.confidence
                    }
                
                # 檢查止盈
                if current_low <= take_profit_price:
                    exit_price = take_profit_price
                    pnl_pct = ((entry_price - exit_price) / entry_price) * 100
                    return {
                        'entry_time': entry_time,
                        'exit_time': current_time,
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'exit_type': 'TAKE_PROFIT',
                        'pnl_pct': pnl_pct,
                        'holding_periods': i - entry_idx,
                        'method': stop_loss_result.method.value,
                        'risk_reward_ratio': stop_loss_result.risk_reward_ratio,
                        'confidence': stop_loss_result.confidence
                    }
        
        # 如果沒有觸發止盈止損，以最後價格平倉
        exit_price = df['close'].iloc[end_idx]
        exit_time = df.index[end_idx]
        
        if direction == 'LONG':
            pnl_pct = ((exit_price - entry_price) / entry_price) * 100
        else:
            pnl_pct = ((entry_price - exit_price) / entry_price) * 100
        
        return {
            'entry_time': entry_time,
            'exit_time': exit_time,
            'entry_price': entry_price,
            'exit_price': exit_price,
            'exit_type': 'TIMEOUT',
            'pnl_pct': pnl_pct,
            'holding_periods': end_idx - entry_idx,
            'method': stop_loss_result.method.value,
            'risk_reward_ratio': stop_loss_result.risk_reward_ratio,
            'confidence': stop_loss_result.confidence
        }

    async def backtest_symbol_timeframe(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """回測單個幣種和時間框架"""
        logger.info(f"🧪 開始回測 {symbol} {timeframe}")

        # 獲取歷史數據
        df = await self.get_bybit_historical_data(symbol, timeframe, days=60)
        if df is None or len(df) < 100:
            logger.error(f"❌ {symbol} {timeframe} 數據不足")
            return None

        # 生成交易信號
        signals = self.generate_rsi_signals(df)
        if not signals:
            logger.warning(f"⚠️ {symbol} {timeframe} 沒有生成交易信號")
            return None

        logger.info(f"📊 {symbol} {timeframe} 生成 {len(signals)} 個交易信號")

        results = []

        for entry_idx, direction in signals:
            entry_price = df['close'].iloc[entry_idx]

            # 獲取該點的數據切片（用於計算指標）
            data_slice = df.iloc[:entry_idx+1].copy()

            # 獲取所有止盈止損方法
            methods = self.calculator.get_all_stop_loss_methods(
                data_slice, direction, entry_price
            )

            if not methods:
                continue

            # 測試每種方法
            for method_result in methods:
                # 跳過無效結果
                if (pd.isna(method_result.stop_loss_price) or
                    pd.isna(method_result.take_profit_price) or
                    method_result.risk_reward_ratio <= 0):
                    continue

                trade_result = self.simulate_trade(
                    df, entry_idx, direction, method_result
                )

                # 合併結果
                result = {
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'direction': direction,
                    **trade_result
                }

                results.append(result)

        if results:
            logger.info(f"✅ {symbol} {timeframe} 完成 {len(results)} 筆交易測試")
            return pd.DataFrame(results)
        else:
            logger.warning(f"⚠️ {symbol} {timeframe} 沒有有效的交易結果")
            return None

    async def run_comprehensive_backtest(self) -> Tuple[Optional[pd.DataFrame], Optional[Dict]]:
        """運行全面回測"""
        logger.info("🚀 開始真實數據止盈止損方法回測")
        logger.info("=" * 60)

        all_results = []

        # 測試所有幣種和時間框架組合
        for symbol in self.test_symbols:
            for timeframe in self.test_timeframes:
                try:
                    result_df = await self.backtest_symbol_timeframe(symbol, timeframe)
                    if result_df is not None and not result_df.empty:
                        all_results.append(result_df)

                except Exception as e:
                    logger.error(f"❌ {symbol} {timeframe} 回測出錯: {e}")
                    continue

                # 避免請求過於頻繁
                await asyncio.sleep(1)

        if not all_results:
            logger.error("❌ 沒有獲得任何回測結果")
            return None, None

        # 合併所有結果
        combined_results = pd.concat(all_results, ignore_index=True)

        # 保存原始結果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"{self.results_dir}/real_backtest_results_{timestamp}.csv"
        combined_results.to_csv(results_file, index=False)
        logger.info(f"📊 原始結果已保存: {results_file}")

        # 分析結果
        analysis = self.analyze_backtest_results(combined_results)

        # 生成報告
        self.generate_backtest_report(combined_results, analysis, timestamp)

        return combined_results, analysis

    def analyze_backtest_results(self, results_df: pd.DataFrame) -> Dict:
        """分析回測結果"""
        analysis = {}

        # 總體統計
        total_trades = len(results_df)
        winning_trades = len(results_df[results_df['pnl_pct'] > 0])
        overall_win_rate = winning_trades / total_trades if total_trades > 0 else 0
        overall_avg_pnl = results_df['pnl_pct'].mean()
        overall_total_return = results_df['pnl_pct'].sum()

        analysis['overall'] = {
            'total_trades': total_trades,
            'win_rate': overall_win_rate,
            'avg_pnl': overall_avg_pnl,
            'total_return': overall_total_return,
            'max_drawdown': results_df['pnl_pct'].min(),
            'best_trade': results_df['pnl_pct'].max()
        }

        # 按方法分析
        analysis['by_method'] = {}
        for method in results_df['method'].unique():
            method_data = results_df[results_df['method'] == method]

            method_total = len(method_data)
            method_wins = len(method_data[method_data['pnl_pct'] > 0])
            method_win_rate = method_wins / method_total if method_total > 0 else 0

            avg_win = method_data[method_data['pnl_pct'] > 0]['pnl_pct'].mean()
            avg_loss = method_data[method_data['pnl_pct'] < 0]['pnl_pct'].mean()

            tp_rate = len(method_data[method_data['exit_type'] == 'TAKE_PROFIT']) / method_total
            sl_rate = len(method_data[method_data['exit_type'] == 'STOP_LOSS']) / method_total

            analysis['by_method'][method] = {
                'total_trades': method_total,
                'win_rate': method_win_rate,
                'avg_pnl': method_data['pnl_pct'].mean(),
                'avg_win': avg_win if not pd.isna(avg_win) else 0,
                'avg_loss': avg_loss if not pd.isna(avg_loss) else 0,
                'total_return': method_data['pnl_pct'].sum(),
                'max_drawdown': method_data['pnl_pct'].min(),
                'take_profit_rate': tp_rate,
                'stop_loss_rate': sl_rate,
                'avg_risk_reward': method_data['risk_reward_ratio'].mean(),
                'avg_confidence': method_data['confidence'].mean(),
                'avg_holding_periods': method_data['holding_periods'].mean()
            }

        # 按幣種分析
        analysis['by_symbol'] = {}
        for symbol in results_df['symbol'].unique():
            symbol_data = results_df[results_df['symbol'] == symbol]

            symbol_total = len(symbol_data)
            symbol_wins = len(symbol_data[symbol_data['pnl_pct'] > 0])
            symbol_win_rate = symbol_wins / symbol_total if symbol_total > 0 else 0

            analysis['by_symbol'][symbol] = {
                'total_trades': symbol_total,
                'win_rate': symbol_win_rate,
                'avg_pnl': symbol_data['pnl_pct'].mean(),
                'total_return': symbol_data['pnl_pct'].sum()
            }

        # 按時間框架分析
        analysis['by_timeframe'] = {}
        for timeframe in results_df['timeframe'].unique():
            tf_data = results_df[results_df['timeframe'] == timeframe]

            tf_total = len(tf_data)
            tf_wins = len(tf_data[tf_data['pnl_pct'] > 0])
            tf_win_rate = tf_wins / tf_total if tf_total > 0 else 0

            analysis['by_timeframe'][timeframe] = {
                'total_trades': tf_total,
                'win_rate': tf_win_rate,
                'avg_pnl': tf_data['pnl_pct'].mean(),
                'total_return': tf_data['pnl_pct'].sum()
            }

        return analysis

    def generate_backtest_report(self, results_df: pd.DataFrame, analysis: Dict, timestamp: str):
        """生成回測報告"""
        print("\n" + "="*80)
        print("📈 真實數據止盈止損方法回測報告")
        print("="*80)

        # 總體統計
        overall = analysis['overall']
        print(f"\n📊 總體統計:")
        print(f"   總交易數: {overall['total_trades']}")
        print(f"   整體勝率: {overall['win_rate']:.2%}")
        print(f"   平均盈虧: {overall['avg_pnl']:.2f}%")
        print(f"   總回報: {overall['total_return']:.2f}%")
        print(f"   最大回撤: {overall['max_drawdown']:.2f}%")
        print(f"   最佳交易: {overall['best_trade']:.2f}%")

        # 方法比較
        print(f"\n🔍 方法比較:")
        print("-" * 80)
        method_comparison = []
        for method, stats in analysis['by_method'].items():
            method_comparison.append({
                '方法': method,
                '交易數': stats['total_trades'],
                '勝率': f"{stats['win_rate']:.1%}",
                '平均盈虧': f"{stats['avg_pnl']:.2f}%",
                '總回報': f"{stats['total_return']:.2f}%",
                '平均盈利': f"{stats['avg_win']:.2f}%",
                '平均虧損': f"{stats['avg_loss']:.2f}%",
                '止盈率': f"{stats['take_profit_rate']:.1%}",
                '風險回報比': f"{stats['avg_risk_reward']:.2f}",
                '平均持倉': f"{stats['avg_holding_periods']:.1f}"
            })

        method_df = pd.DataFrame(method_comparison)
        print(method_df.to_string(index=False))

        # 幣種表現
        print(f"\n💰 幣種表現:")
        print("-" * 50)
        symbol_comparison = []
        for symbol, stats in analysis['by_symbol'].items():
            symbol_comparison.append({
                '幣種': symbol,
                '交易數': stats['total_trades'],
                '勝率': f"{stats['win_rate']:.1%}",
                '平均盈虧': f"{stats['avg_pnl']:.2f}%",
                '總回報': f"{stats['total_return']:.2f}%"
            })

        symbol_df = pd.DataFrame(symbol_comparison)
        print(symbol_df.to_string(index=False))

        # 時間框架比較
        print(f"\n⏰ 時間框架比較:")
        print("-" * 50)
        tf_comparison = []
        for tf, stats in analysis['by_timeframe'].items():
            tf_comparison.append({
                '時間框架': tf,
                '交易數': stats['total_trades'],
                '勝率': f"{stats['win_rate']:.1%}",
                '平均盈虧': f"{stats['avg_pnl']:.2f}%",
                '總回報': f"{stats['total_return']:.2f}%"
            })

        tf_df = pd.DataFrame(tf_comparison)
        print(tf_df.to_string(index=False))

        # 推薦最佳方法
        best_methods = self.get_best_methods(analysis['by_method'])
        print(f"\n🏆 推薦結果:")
        print("-" * 50)
        print(f"🥇 最高勝率: {best_methods['best_winrate']['method']} ({best_methods['best_winrate']['win_rate']:.1%})")
        print(f"💰 最高總回報: {best_methods['best_return']['method']} ({best_methods['best_return']['total_return']:.2f}%)")
        print(f"⚖️ 最佳風險回報比: {best_methods['best_risk_reward']['method']} ({best_methods['best_risk_reward']['avg_risk_reward']:.2f})")
        print(f"🎯 綜合最佳: {best_methods['overall_best']['method']} (評分: {best_methods['overall_best']['score']:.3f})")

        # 保存詳細報告
        report_file = f"{self.results_dir}/backtest_report_{timestamp}.json"
        report_data = {
            'timestamp': timestamp,
            'analysis': analysis,
            'best_methods': best_methods,
            'test_config': {
                'symbols': self.test_symbols,
                'timeframes': self.test_timeframes,
                'data_source': 'Bybit Historical Data'
            }
        }

        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)

        print(f"\n📄 詳細報告已保存: {report_file}")

    def get_best_methods(self, method_analysis: Dict) -> Dict:
        """獲取最佳方法"""
        methods_list = [(method, stats) for method, stats in method_analysis.items()]

        # 按不同標準排序
        best_winrate = max(methods_list, key=lambda x: x[1]['win_rate'])
        best_return = max(methods_list, key=lambda x: x[1]['total_return'])
        best_risk_reward = max(methods_list, key=lambda x: x[1]['avg_risk_reward'])

        # 綜合評分
        scored_methods = []
        for method, stats in methods_list:
            # 綜合評分公式
            score = (
                stats['win_rate'] * 0.3 +
                min(stats['total_return'] / 100, 1.0) * 0.25 +  # 標準化總回報
                min(stats['avg_risk_reward'] / 5, 1.0) * 0.2 +  # 標準化風險回報比
                stats['take_profit_rate'] * 0.15 +  # 止盈率
                (1 - abs(stats['avg_loss']) / 10) * 0.1  # 虧損控制
            )
            scored_methods.append((method, stats, score))

        overall_best = max(scored_methods, key=lambda x: x[2])

        return {
            'best_winrate': {'method': best_winrate[0], **best_winrate[1]},
            'best_return': {'method': best_return[0], **best_return[1]},
            'best_risk_reward': {'method': best_risk_reward[0], **best_risk_reward[1]},
            'overall_best': {'method': overall_best[0], 'score': overall_best[2], **overall_best[1]}
        }

async def main():
    """主函數"""
    print("🎯 真實數據止盈止損方法回測系統")
    print("=" * 60)
    print("📊 數據源: Bybit歷史K線數據")
    print("🔍 測試幣種: BTC, ETH, SOL, XRP, 1000PEPE")
    print("⏰ 測試時間框架: 1H, 4H")
    print("📈 測試方法: ATR, VWAP, Bollinger, SAR, Supertrend, 支撐阻力, 多指標綜合")
    print("=" * 60)

    async with RealDataBacktester() as backtester:
        try:
            results, analysis = await backtester.run_comprehensive_backtest()

            if results is not None and analysis is not None:
                print("\n✅ 回測完成！")
                print(f"📁 結果文件保存在 {backtester.results_dir}/ 目錄中")
                print(f"📊 總共測試了 {len(results)} 筆交易")
            else:
                print("\n❌ 回測失敗，無法獲取足夠的數據")

        except Exception as e:
            logger.error(f"❌ 回測過程中出現錯誤: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
